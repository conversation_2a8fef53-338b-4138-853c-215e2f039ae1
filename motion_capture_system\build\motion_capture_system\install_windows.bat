@echo off
chcp 65001 >nul
title 实时动作捕捉和生成系统 - 安装器 v1.0.0

echo.
echo ================================================================
echo 🎭 实时动作捕捉和生成系统 - 自动安装器
echo ================================================================
echo 版本: v1.0.0
echo 构建日期: 2025-07-22 16:19:40
echo.

echo 📋 开始安装...
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python检查通过

REM 运行安装脚本
echo.
echo 📦 运行安装脚本...
python install.py

if errorlevel 1 (
    echo.
    echo ❌ 安装失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo ✅ 安装完成！
echo.
echo 🚀 下一步操作:
echo 1. 运行系统优化器: python system_optimizer.py
echo 2. 启动主程序: python main.py
echo 3. 运行演示程序: python demo.py
echo.
echo 📖 更多信息请查看 QUICK_START.md 和 README.md
echo.

pause
