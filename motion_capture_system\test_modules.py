#!/usr/bin/env python3
"""
模块测试脚本
用于验证各个模块的基本功能
"""

import sys
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

import yaml
from loguru import logger

# 导入模块
from src.mediapipe_capture.pose_detector import PoseDetector
from src.mediapipe_capture.data_processor import DataProcessor
from src.mdm_generator.motion_predictor import MotionPredictor
from src.osc_communication.osc_sender import OSCSender
from src.utils.filters import DataFilter
from src.utils.performance import PerformanceMonitor


def load_config():
    """加载配置文件"""
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def test_pose_detector():
    """测试姿态检测器"""
    logger.info("测试姿态检测器...")
    
    try:
        config = load_config()
        detector = PoseDetector(config['camera'], config['mediapipe'])
        
        if not detector.is_camera_opened():
            logger.error("摄像头无法打开")
            return False
        
        # 测试几帧
        for i in range(5):
            pose_data = detector.get_pose()
            if pose_data:
                logger.info(f"帧 {i+1}: 检测到姿态={pose_data['has_pose']}, FPS={pose_data['fps']:.1f}")
            else:
                logger.warning(f"帧 {i+1}: 无法获取姿态数据")
            
            time.sleep(0.1)
        
        detector.cleanup()
        logger.info("姿态检测器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"姿态检测器测试失败: {e}")
        return False


def test_data_processor():
    """测试数据处理器"""
    logger.info("测试数据处理器...")
    
    try:
        config = load_config()
        processor = DataProcessor(config['data_processing'])
        
        # 创建模拟姿态数据
        mock_landmarks = []
        for i in range(33):
            landmark = {
                'id': i,
                'name': f'landmark_{i}',
                'x': np.random.uniform(0, 640),
                'y': np.random.uniform(0, 480),
                'z': np.random.uniform(-100, 100),
                'visibility': np.random.uniform(0.5, 1.0),
                'normalized_x': np.random.uniform(0, 1),
                'normalized_y': np.random.uniform(0, 1),
                'normalized_z': np.random.uniform(-1, 1)
            }
            mock_landmarks.append(landmark)
        
        mock_pose_data = {
            'landmarks': mock_landmarks,
            'has_pose': True,
            'timestamp': time.time()
        }
        
        # 处理数据
        processed_data = processor.process_pose_data(mock_pose_data)
        
        logger.info(f"数据处理完成: 关键点数量={len(processed_data['processed_landmarks'])}")
        logger.info(f"运动特征: {list(processed_data['motion_features'].keys())}")
        
        logger.info("数据处理器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"数据处理器测试失败: {e}")
        return False


def test_data_filter():
    """测试数据滤波器"""
    logger.info("测试数据滤波器...")
    
    try:
        config = load_config()
        data_filter = DataFilter(config['data_processing'])
        
        # 创建模拟数据
        mock_landmarks = []
        for i in range(10):
            landmark = {
                'id': i,
                'x': 100 + np.random.normal(0, 5),  # 添加噪声
                'y': 200 + np.random.normal(0, 5),
                'z': 0 + np.random.normal(0, 2),
                'visibility': 0.9,
                'normalized_x': 0.5 + np.random.normal(0, 0.01),
                'normalized_y': 0.5 + np.random.normal(0, 0.01),
                'normalized_z': 0.0 + np.random.normal(0, 0.005)
            }
            mock_landmarks.append(landmark)
        
        mock_pose_data = {
            'landmarks': mock_landmarks,
            'has_pose': True
        }
        
        # 应用滤波
        filtered_data = data_filter.process(mock_pose_data)
        
        logger.info(f"滤波完成: 滤波器数量={data_filter.get_filter_count()}")
        logger.info(f"滤波方法: {filtered_data.get('filter_method', 'N/A')}")
        
        logger.info("数据滤波器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"数据滤波器测试失败: {e}")
        return False


def test_motion_predictor():
    """测试动作预测器"""
    logger.info("测试动作预测器...")
    
    try:
        config = load_config()
        predictor = MotionPredictor(config['mdm'])
        
        # 创建模拟姿态序列
        for frame in range(15):  # 需要足够的帧来填充缓存
            mock_landmarks = []
            for i in range(33):
                # 模拟简单的运动（左右摆动）
                x = 320 + 50 * np.sin(frame * 0.1 + i * 0.1)
                y = 240 + 20 * np.cos(frame * 0.1 + i * 0.1)
                z = 0 + 10 * np.sin(frame * 0.05)
                
                landmark = {
                    'id': i,
                    'x': x,
                    'y': y,
                    'z': z,
                    'visibility': 0.9,
                    'normalized_x': x / 640,
                    'normalized_y': y / 480,
                    'normalized_z': z / 100
                }
                mock_landmarks.append(landmark)
            
            mock_pose_data = {
                'landmarks': mock_landmarks,
                'has_pose': True,
                'timestamp': time.time()
            }
            
            # 预测
            enhanced_data = predictor.predict(mock_pose_data)
            
            if frame >= 10:  # 缓存填充后开始检查预测
                logger.info(f"帧 {frame}: 预测应用={enhanced_data.get('prediction_applied', False)}")
                if enhanced_data.get('predictions'):
                    pred_info = enhanced_data['predictions']
                    logger.info(f"  预测长度: {pred_info['prediction_length']}, 模型: {pred_info['model_type']}")
        
        # 获取性能统计
        stats = predictor.get_performance_stats()
        logger.info(f"预测统计: {stats}")
        
        logger.info("动作预测器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"动作预测器测试失败: {e}")
        return False


def test_osc_sender():
    """测试OSC发送器"""
    logger.info("测试OSC发送器...")
    
    try:
        config = load_config()
        sender = OSCSender(config['osc'])
        
        # 测试连接
        if not sender.test_connection():
            logger.warning("OSC连接测试失败，但继续测试发送功能")
        
        # 创建模拟数据
        mock_landmarks = []
        for i in range(5):  # 只测试几个关键点
            landmark = {
                'id': i,
                'name': f'test_joint_{i}',
                'x': 100 + i * 50,
                'y': 200 + i * 30,
                'z': i * 10,
                'visibility': 0.9,
                'normalized_x': (100 + i * 50) / 640,
                'normalized_y': (200 + i * 30) / 480,
                'normalized_z': (i * 10) / 100
            }
            mock_landmarks.append(landmark)
        
        mock_pose_data = {
            'landmarks': mock_landmarks,
            'has_pose': True,
            'timestamp': time.time(),
            'frame_count': 123,
            'fps': 30.0
        }
        
        # 发送数据
        success = sender.send_pose_data(mock_pose_data)
        logger.info(f"OSC发送结果: {success}")
        
        # 获取统计信息
        stats = sender.get_statistics()
        logger.info(f"OSC统计: {stats}")
        
        sender.cleanup()
        logger.info("OSC发送器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"OSC发送器测试失败: {e}")
        return False


def test_performance_monitor():
    """测试性能监控器"""
    logger.info("测试性能监控器...")
    
    try:
        config = load_config()
        monitor = PerformanceMonitor(config['logging']['performance_monitoring'])
        
        # 模拟一些性能数据
        for i in range(10):
            monitor.record_frame(f"frame_{i}")
            monitor.start_processing(f"process_{i}")
            
            # 模拟处理时间
            time.sleep(0.01)
            
            monitor.end_processing(f"process_{i}")
        
        # 收集指标
        metrics = monitor.collect_metrics()
        logger.info(f"性能指标: {list(metrics.keys())}")
        
        if 'fps' in metrics:
            logger.info(f"FPS: 当前={metrics['fps']['current']:.1f}, 平均={metrics['fps']['average']:.1f}")
        
        if 'latency_ms' in metrics:
            logger.info(f"延迟: 平均={metrics['latency_ms']['average']:.1f}ms, P95={metrics['latency_ms']['p95']:.1f}ms")
        
        # 获取性能摘要
        summary = monitor.get_performance_summary()
        logger.info(f"性能状态: {summary['status']}")
        if summary['warnings']:
            logger.warning(f"警告: {summary['warnings']}")
        
        logger.info("性能监控器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"性能监控器测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始模块测试...")
    
    tests = [
        ("姿态检测器", test_pose_detector),
        ("数据处理器", test_data_processor),
        ("数据滤波器", test_data_filter),
        ("动作预测器", test_motion_predictor),
        ("OSC发送器", test_osc_sender),
        ("性能监控器", test_performance_monitor)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"{test_name}测试出现异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    logger.info(f"\n{'='*50}")
    logger.info("测试结果汇总")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！系统准备就绪。")
    else:
        logger.warning(f"⚠️  有 {total - passed} 个测试失败，请检查相关模块。")


if __name__ == "__main__":
    main()
