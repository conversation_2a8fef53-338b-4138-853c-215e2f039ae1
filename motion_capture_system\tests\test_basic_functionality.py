#!/usr/bin/env python3
"""
基本功能单元测试
"""

import unittest
import sys
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

import yaml
from src.mediapipe_capture.data_processor import DataProcessor
from src.mdm_generator.motion_predictor import MotionPredictor
from src.utils.filters import DataFilter, KalmanFilter, MovingAverageFilter
from src.utils.performance import PerformanceMonitor
from src.utils.error_handler import ErrorHandler, ErrorType, ErrorSeverity


class TestDataProcessor(unittest.TestCase):
    """测试数据处理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            'selected_keypoints': list(range(10)),  # 只测试前10个关键点
            'normalization': {
                'enabled': True,
                'method': 'minmax'
            }
        }
        self.processor = DataProcessor(self.config)
    
    def test_process_pose_data(self):
        """测试姿态数据处理"""
        # 创建模拟数据
        landmarks = []
        for i in range(10):
            landmark = {
                'id': i,
                'name': f'landmark_{i}',
                'x': 100 + i * 10,
                'y': 200 + i * 5,
                'z': i * 2,
                'visibility': 0.9,
                'normalized_x': (100 + i * 10) / 640,
                'normalized_y': (200 + i * 5) / 480,
                'normalized_z': (i * 2) / 100
            }
            landmarks.append(landmark)
        
        pose_data = {
            'landmarks': landmarks,
            'has_pose': True,
            'timestamp': time.time()
        }
        
        # 处理数据
        processed_data = self.processor.process_pose_data(pose_data)
        
        # 验证结果
        self.assertTrue('processed_landmarks' in processed_data)
        self.assertTrue('motion_features' in processed_data)
        self.assertEqual(len(processed_data['processed_landmarks']), 10)
    
    def test_empty_pose_data(self):
        """测试空姿态数据"""
        pose_data = {
            'landmarks': None,
            'has_pose': False
        }
        
        processed_data = self.processor.process_pose_data(pose_data)
        self.assertEqual(processed_data, pose_data)


class TestDataFilter(unittest.TestCase):
    """测试数据滤波器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            'smoothing': {
                'enabled': True,
                'method': 'kalman',
                'window_size': 5,
                'alpha': 0.3
            }
        }
        self.filter = DataFilter(self.config)
    
    def test_kalman_filter(self):
        """测试卡尔曼滤波"""
        kalman = KalmanFilter()
        
        # 测试数据
        measurements = [1.0, 1.1, 0.9, 1.05, 0.95]
        filtered_values = []
        
        for measurement in measurements:
            filtered_value = kalman.update(measurement)
            filtered_values.append(filtered_value)
        
        # 验证滤波效果
        self.assertEqual(len(filtered_values), len(measurements))
        self.assertIsInstance(filtered_values[0], float)
    
    def test_moving_average_filter(self):
        """测试移动平均滤波"""
        ma_filter = MovingAverageFilter(window_size=3)
        
        values = [1.0, 2.0, 3.0, 4.0, 5.0]
        filtered_values = []
        
        for value in values:
            filtered_value = ma_filter.update(value)
            filtered_values.append(filtered_value)
        
        # 验证结果
        self.assertEqual(len(filtered_values), len(values))
        self.assertAlmostEqual(filtered_values[-1], 4.0)  # (3+4+5)/3 = 4
    
    def test_filter_pose_data(self):
        """测试姿态数据滤波"""
        # 创建模拟数据
        landmarks = []
        for i in range(5):
            landmark = {
                'id': i,
                'x': 100 + np.random.normal(0, 1),  # 添加噪声
                'y': 200 + np.random.normal(0, 1),
                'z': i + np.random.normal(0, 0.1),
                'visibility': 0.9,
                'normalized_x': 0.5 + np.random.normal(0, 0.01),
                'normalized_y': 0.5 + np.random.normal(0, 0.01),
                'normalized_z': 0.0 + np.random.normal(0, 0.001)
            }
            landmarks.append(landmark)
        
        pose_data = {
            'landmarks': landmarks,
            'has_pose': True
        }
        
        # 应用滤波
        filtered_data = self.filter.process(pose_data)
        
        # 验证结果
        self.assertTrue('filtered' in filtered_data)
        self.assertTrue(filtered_data['filtered'])
        self.assertEqual(len(filtered_data['landmarks']), len(landmarks))


class TestMotionPredictor(unittest.TestCase):
    """测试动作预测器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            'enabled': True,
            'model_type': 'simple',
            'sequence_length': 5,
            'prediction_length': 3
        }
        self.predictor = MotionPredictor(self.config)
    
    def test_feature_extraction(self):
        """测试特征提取"""
        landmarks = []
        for i in range(10):
            landmark = {
                'id': i,
                'x': 100 + i,
                'y': 200 + i,
                'z': i,
                'visibility': 0.9,
                'normalized_x': (100 + i) / 640,
                'normalized_y': (200 + i) / 480,
                'normalized_z': i / 100
            }
            landmarks.append(landmark)
        
        pose_data = {
            'landmarks': landmarks,
            'has_pose': True
        }
        
        features = self.predictor._extract_features(pose_data)
        
        # 验证特征向量
        self.assertIsNotNone(features)
        self.assertEqual(len(features), 10 * 3)  # 10个关键点 * 3个坐标
        self.assertIsInstance(features, np.ndarray)
    
    def test_prediction_buffer(self):
        """测试预测缓存"""
        # 填充缓存
        for i in range(10):
            landmarks = []
            for j in range(5):
                landmark = {
                    'id': j,
                    'x': 100 + i + j,
                    'y': 200 + i + j,
                    'z': i + j,
                    'visibility': 0.9,
                    'normalized_x': (100 + i + j) / 640,
                    'normalized_y': (200 + i + j) / 480,
                    'normalized_z': (i + j) / 100
                }
                landmarks.append(landmark)
            
            pose_data = {
                'landmarks': landmarks,
                'has_pose': True,
                'timestamp': time.time()
            }
            
            result = self.predictor.predict(pose_data)
            
            # 检查缓存状态
            buffer_status = self.predictor.get_buffer_status()
            self.assertIsInstance(buffer_status, dict)
            self.assertIn('buffer_size', buffer_status)
        
        # 验证缓存已满
        self.assertTrue(self.predictor.is_ready())


class TestPerformanceMonitor(unittest.TestCase):
    """测试性能监控器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            'enabled': True,
            'log_interval': 1,
            'metrics': ['fps', 'latency', 'cpu_usage', 'memory_usage']
        }
        self.monitor = PerformanceMonitor(self.config)
    
    def test_frame_recording(self):
        """测试帧记录"""
        # 记录几帧
        for i in range(10):
            self.monitor.record_frame(f"frame_{i}")
            time.sleep(0.01)
        
        # 收集指标
        metrics = self.monitor.collect_metrics()
        
        # 验证FPS指标
        self.assertIn('fps', metrics)
        self.assertGreater(metrics['fps']['current'], 0)
    
    def test_latency_tracking(self):
        """测试延迟跟踪"""
        # 模拟处理过程
        for i in range(5):
            process_id = f"process_{i}"
            self.monitor.start_processing(process_id)
            time.sleep(0.01)  # 模拟处理时间
            self.monitor.end_processing(process_id)
        
        # 收集指标
        metrics = self.monitor.collect_metrics()
        
        # 验证延迟指标
        self.assertIn('latency_ms', metrics)
        self.assertGreater(metrics['latency_ms']['average'], 0)
    
    def test_performance_summary(self):
        """测试性能摘要"""
        # 记录一些数据
        for i in range(5):
            self.monitor.record_frame(f"frame_{i}")
            self.monitor.start_processing(f"process_{i}")
            time.sleep(0.005)
            self.monitor.end_processing(f"process_{i}")
        
        # 获取摘要
        summary = self.monitor.get_performance_summary()
        
        # 验证摘要结构
        self.assertIn('status', summary)
        self.assertIn('warnings', summary)
        self.assertIn('errors', summary)
        self.assertIn('metrics', summary)


class TestErrorHandler(unittest.TestCase):
    """测试错误处理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            'error_rate_threshold': 5,
            'critical_error_threshold': 3,
            'monitoring_enabled': True,
            'auto_recovery_enabled': True
        }
        self.error_handler = ErrorHandler(self.config)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 处理一个错误
        result = self.error_handler.handle_error(
            error_type=ErrorType.PROCESSING_ERROR,
            severity=ErrorSeverity.MEDIUM,
            message="测试错误",
            context={'test': True}
        )
        
        # 验证错误记录
        stats = self.error_handler.get_error_statistics()
        self.assertGreater(stats['total_errors'], 0)
        self.assertIn(ErrorType.PROCESSING_ERROR, stats['error_counts_by_type'])
    
    def test_recovery_action(self):
        """测试恢复动作"""
        recovery_called = False
        
        def mock_recovery():
            nonlocal recovery_called
            recovery_called = True
            return True
        
        # 注册恢复动作
        self.error_handler.register_recovery_action(
            error_type=ErrorType.CAMERA_ERROR,
            name="mock_recovery",
            action=mock_recovery
        )
        
        # 触发错误
        self.error_handler.handle_error(
            error_type=ErrorType.CAMERA_ERROR,
            severity=ErrorSeverity.HIGH,
            message="摄像头错误"
        )
        
        # 验证恢复动作被调用
        self.assertTrue(recovery_called)
    
    def test_error_statistics(self):
        """测试错误统计"""
        # 生成多个错误
        for i in range(3):
            self.error_handler.handle_error(
                error_type=ErrorType.NETWORK_ERROR,
                severity=ErrorSeverity.LOW,
                message=f"网络错误 {i}"
            )
        
        # 获取统计信息
        stats = self.error_handler.get_error_statistics()
        
        # 验证统计数据
        self.assertEqual(stats['total_errors'], 3)
        self.assertEqual(stats['error_counts_by_type'][ErrorType.NETWORK_ERROR], 3)


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)
