#!/usr/bin/env python3
"""
系统启动脚本
提供简化的启动接口和调试功能
"""

import argparse
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from main import MotionCaptureSystem
from loguru import logger


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='实时动作捕捉和生成系统')
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/config.yaml',
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--no-video',
        action='store_true',
        help='禁用视频显示'
    )
    
    parser.add_argument(
        '--no-osc',
        action='store_true',
        help='禁用OSC发送'
    )
    
    parser.add_argument(
        '--no-prediction',
        action='store_true',
        help='禁用动作预测'
    )
    
    parser.add_argument(
        '--model-type',
        type=str,
        choices=['simple', 'lstm', 'transformer'],
        help='指定动作预测模型类型'
    )
    
    parser.add_argument(
        '--camera-id',
        type=int,
        default=0,
        help='摄像头设备ID'
    )
    
    parser.add_argument(
        '--osc-port',
        type=int,
        help='OSC端口号'
    )
    
    parser.add_argument(
        '--fps',
        type=int,
        help='目标帧率'
    )
    
    return parser.parse_args()


def modify_config_from_args(config, args):
    """根据命令行参数修改配置"""
    if args.debug:
        config['logging']['level'] = 'DEBUG'
        config['debug']['show_video'] = True
        config['debug']['show_landmarks'] = True
    
    if args.no_video:
        config['debug']['show_video'] = False
    
    if args.no_osc:
        config['osc']['enabled'] = False
    
    if args.no_prediction:
        config['mdm']['enabled'] = False
    
    if args.model_type:
        config['mdm']['model_type'] = args.model_type
    
    if args.camera_id is not None:
        config['camera']['device_id'] = args.camera_id
    
    if args.osc_port:
        config['osc']['port'] = args.osc_port
    
    if args.fps:
        config['camera']['fps'] = args.fps
    
    return config


async def main():
    """主函数"""
    args = parse_arguments()
    
    try:
        # 创建系统实例
        system = MotionCaptureSystem(args.config)
        
        # 根据命令行参数修改配置
        system.config = modify_config_from_args(system.config, args)
        
        # 显示启动信息
        logger.info("=" * 60)
        logger.info("实时动作捕捉和生成系统")
        logger.info("=" * 60)
        logger.info(f"配置文件: {args.config}")
        logger.info(f"摄像头ID: {system.config['camera']['device_id']}")
        logger.info(f"目标FPS: {system.config['camera']['fps']}")
        logger.info(f"OSC端口: {system.config['osc']['port']}")
        logger.info(f"动作预测: {'启用' if system.config['mdm']['enabled'] else '禁用'}")
        logger.info(f"模型类型: {system.config['mdm']['model_type']}")
        logger.info("=" * 60)
        
        # 启动系统
        await system.start()
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在停止系统...")
    except Exception as e:
        logger.error(f"系统运行错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'system' in locals():
            await system.stop()


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主程序
    asyncio.run(main())
