#!/bin/bash

# 实时动作捕捉和生成系统 - 安装器 v1.0.0

echo "================================================================"
echo "🎭 实时动作捕捉和生成系统 - 自动安装器"
echo "================================================================"
echo "版本: v1.0.0"
echo "构建日期: 2025-07-22 16:19:40"
echo ""

echo "📋 开始安装..."
echo ""

# 检查Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 错误: 未找到Python，请先安装Python 3.8或更高版本"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python检查通过"

# 运行安装脚本
echo ""
echo "📦 运行安装脚本..."
$PYTHON_CMD install.py

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ 安装失败，请检查错误信息"
    exit 1
fi

echo ""
echo "✅ 安装完成！"
echo ""
echo "🚀 下一步操作:"
echo "1. 运行系统优化器: $PYTHON_CMD system_optimizer.py"
echo "2. 启动主程序: $PYTHON_CMD main.py"
echo "3. 运行演示程序: $PYTHON_CMD demo.py"
echo ""
echo "📖 更多信息请查看 QUICK_START.md 和 README.md"
echo ""
