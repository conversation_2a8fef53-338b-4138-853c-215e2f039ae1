#!/usr/bin/env python3
"""
性能基准测试脚本
测试系统在不同配置下的性能表现
"""

import sys
import time
import asyncio
import statistics
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    import yaml
    import numpy as np
    from loguru import logger
    
    # 导入系统模块
    from src.mediapipe_capture.data_processor import DataProcessor
    from src.mdm_generator.motion_predictor import MotionPredictor
    from src.utils.filters import DataFilter
    from src.utils.performance import PerformanceMonitor
    
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"警告: 某些依赖不可用: {e}")
    print("将运行简化的基准测试")
    DEPENDENCIES_AVAILABLE = False


class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self):
        """初始化基准测试"""
        self.results = {}
        
    def load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            return {}
    
    def create_mock_pose_data(self, num_landmarks: int = 33) -> Dict[str, Any]:
        """创建模拟姿态数据"""
        landmarks = []
        for i in range(num_landmarks):
            landmark = {
                'id': i,
                'name': f'landmark_{i}',
                'x': 100 + i * 10 + np.random.normal(0, 2) if DEPENDENCIES_AVAILABLE else 100 + i * 10,
                'y': 200 + i * 5 + np.random.normal(0, 1) if DEPENDENCIES_AVAILABLE else 200 + i * 5,
                'z': i * 2 + np.random.normal(0, 0.5) if DEPENDENCIES_AVAILABLE else i * 2,
                'visibility': 0.9,
                'normalized_x': (100 + i * 10) / 640,
                'normalized_y': (200 + i * 5) / 480,
                'normalized_z': (i * 2) / 100
            }
            landmarks.append(landmark)
        
        return {
            'landmarks': landmarks,
            'has_pose': True,
            'timestamp': time.time(),
            'frame_count': 0,
            'fps': 30.0
        }
    
    def benchmark_data_processing(self, config: Dict, iterations: int = 1000) -> Dict[str, float]:
        """基准测试数据处理性能"""
        if not DEPENDENCIES_AVAILABLE:
            return {'error': 'Dependencies not available'}
        
        print(f"基准测试数据处理 ({iterations} 次迭代)...")
        
        processor = DataProcessor(config.get('data_processing', {}))
        times = []
        
        for i in range(iterations):
            pose_data = self.create_mock_pose_data()
            
            start_time = time.perf_counter()
            processed_data = processor.process_pose_data(pose_data)
            end_time = time.perf_counter()
            
            times.append((end_time - start_time) * 1000)  # 转换为毫秒
        
        return {
            'avg_time_ms': statistics.mean(times),
            'min_time_ms': min(times),
            'max_time_ms': max(times),
            'std_time_ms': statistics.stdev(times) if len(times) > 1 else 0,
            'p95_time_ms': sorted(times)[int(len(times) * 0.95)],
            'p99_time_ms': sorted(times)[int(len(times) * 0.99)]
        }
    
    def benchmark_data_filtering(self, config: Dict, iterations: int = 1000) -> Dict[str, float]:
        """基准测试数据滤波性能"""
        if not DEPENDENCIES_AVAILABLE:
            return {'error': 'Dependencies not available'}
        
        print(f"基准测试数据滤波 ({iterations} 次迭代)...")
        
        data_filter = DataFilter(config.get('data_processing', {}))
        times = []
        
        for i in range(iterations):
            pose_data = self.create_mock_pose_data()
            
            start_time = time.perf_counter()
            filtered_data = data_filter.process(pose_data)
            end_time = time.perf_counter()
            
            times.append((end_time - start_time) * 1000)
        
        return {
            'avg_time_ms': statistics.mean(times),
            'min_time_ms': min(times),
            'max_time_ms': max(times),
            'std_time_ms': statistics.stdev(times) if len(times) > 1 else 0,
            'p95_time_ms': sorted(times)[int(len(times) * 0.95)],
            'p99_time_ms': sorted(times)[int(len(times) * 0.99)]
        }
    
    def benchmark_motion_prediction(self, config: Dict, iterations: int = 100) -> Dict[str, float]:
        """基准测试动作预测性能"""
        if not DEPENDENCIES_AVAILABLE:
            return {'error': 'Dependencies not available'}
        
        print(f"基准测试动作预测 ({iterations} 次迭代)...")
        
        predictor = MotionPredictor(config.get('mdm', {}))
        times = []
        
        # 预热预测器
        for i in range(10):
            pose_data = self.create_mock_pose_data()
            predictor.predict(pose_data)
        
        # 实际测试
        for i in range(iterations):
            pose_data = self.create_mock_pose_data()
            
            start_time = time.perf_counter()
            predicted_data = predictor.predict(pose_data)
            end_time = time.perf_counter()
            
            times.append((end_time - start_time) * 1000)
        
        return {
            'avg_time_ms': statistics.mean(times),
            'min_time_ms': min(times),
            'max_time_ms': max(times),
            'std_time_ms': statistics.stdev(times) if len(times) > 1 else 0,
            'p95_time_ms': sorted(times)[int(len(times) * 0.95)],
            'p99_time_ms': sorted(times)[int(len(times) * 0.99)]
        }
    
    def benchmark_end_to_end_latency(self, config: Dict, iterations: int = 100) -> Dict[str, float]:
        """基准测试端到端延迟"""
        if not DEPENDENCIES_AVAILABLE:
            return {'error': 'Dependencies not available'}
        
        print(f"基准测试端到端延迟 ({iterations} 次迭代)...")
        
        # 初始化所有组件
        processor = DataProcessor(config.get('data_processing', {}))
        data_filter = DataFilter(config.get('data_processing', {}))
        predictor = MotionPredictor(config.get('mdm', {}))
        
        # 预热
        for i in range(10):
            pose_data = self.create_mock_pose_data()
            processed_data = processor.process_pose_data(pose_data)
            filtered_data = data_filter.process(processed_data)
            predicted_data = predictor.predict(filtered_data)
        
        times = []
        
        for i in range(iterations):
            pose_data = self.create_mock_pose_data()
            
            start_time = time.perf_counter()
            
            # 完整的处理流程
            processed_data = processor.process_pose_data(pose_data)
            filtered_data = data_filter.process(processed_data)
            predicted_data = predictor.predict(filtered_data)
            
            end_time = time.perf_counter()
            
            times.append((end_time - start_time) * 1000)
        
        return {
            'avg_time_ms': statistics.mean(times),
            'min_time_ms': min(times),
            'max_time_ms': max(times),
            'std_time_ms': statistics.stdev(times) if len(times) > 1 else 0,
            'p95_time_ms': sorted(times)[int(len(times) * 0.95)],
            'p99_time_ms': sorted(times)[int(len(times) * 0.99)],
            'target_met': statistics.mean(times) < config.get('performance', {}).get('target_latency_ms', 100)
        }
    
    def benchmark_memory_usage(self, config: Dict) -> Dict[str, Any]:
        """基准测试内存使用"""
        try:
            import psutil
            import gc
            
            process = psutil.Process()
            
            # 初始内存
            gc.collect()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            if DEPENDENCIES_AVAILABLE:
                # 创建组件
                processor = DataProcessor(config.get('data_processing', {}))
                data_filter = DataFilter(config.get('data_processing', {}))
                predictor = MotionPredictor(config.get('mdm', {}))
                
                # 运行一些操作
                for i in range(100):
                    pose_data = self.create_mock_pose_data()
                    processed_data = processor.process_pose_data(pose_data)
                    filtered_data = data_filter.process(processed_data)
                    predicted_data = predictor.predict(filtered_data)
            
            # 最终内存
            gc.collect()
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            return {
                'initial_memory_mb': initial_memory,
                'final_memory_mb': final_memory,
                'memory_increase_mb': final_memory - initial_memory,
                'peak_memory_mb': final_memory
            }
            
        except ImportError:
            return {'error': 'psutil not available'}
    
    def run_benchmark_suite(self, config_path: str) -> Dict[str, Any]:
        """运行完整的基准测试套件"""
        config = self.load_config(config_path)
        
        print(f"运行基准测试套件: {config_path}")
        print("=" * 60)
        
        results = {
            'config_path': config_path,
            'timestamp': time.time(),
            'dependencies_available': DEPENDENCIES_AVAILABLE
        }
        
        if DEPENDENCIES_AVAILABLE:
            # 数据处理基准测试
            results['data_processing'] = self.benchmark_data_processing(config)
            
            # 数据滤波基准测试
            results['data_filtering'] = self.benchmark_data_filtering(config)
            
            # 动作预测基准测试
            results['motion_prediction'] = self.benchmark_motion_prediction(config)
            
            # 端到端延迟基准测试
            results['end_to_end_latency'] = self.benchmark_end_to_end_latency(config)
            
            # 内存使用基准测试
            results['memory_usage'] = self.benchmark_memory_usage(config)
        else:
            results['error'] = 'Dependencies not available for full benchmark'
        
        return results
    
    def print_results(self, results: Dict[str, Any]):
        """打印基准测试结果"""
        print("\n" + "=" * 60)
        print("基准测试结果")
        print("=" * 60)
        
        if 'error' in results:
            print(f"错误: {results['error']}")
            return
        
        # 数据处理结果
        if 'data_processing' in results:
            dp = results['data_processing']
            print(f"\n数据处理性能:")
            print(f"  平均时间: {dp['avg_time_ms']:.2f}ms")
            print(f"  P95时间: {dp['p95_time_ms']:.2f}ms")
            print(f"  P99时间: {dp['p99_time_ms']:.2f}ms")
        
        # 数据滤波结果
        if 'data_filtering' in results:
            df = results['data_filtering']
            print(f"\n数据滤波性能:")
            print(f"  平均时间: {df['avg_time_ms']:.2f}ms")
            print(f"  P95时间: {df['p95_time_ms']:.2f}ms")
            print(f"  P99时间: {df['p99_time_ms']:.2f}ms")
        
        # 动作预测结果
        if 'motion_prediction' in results:
            mp = results['motion_prediction']
            print(f"\n动作预测性能:")
            print(f"  平均时间: {mp['avg_time_ms']:.2f}ms")
            print(f"  P95时间: {mp['p95_time_ms']:.2f}ms")
            print(f"  P99时间: {mp['p99_time_ms']:.2f}ms")
        
        # 端到端延迟结果
        if 'end_to_end_latency' in results:
            e2e = results['end_to_end_latency']
            print(f"\n端到端延迟:")
            print(f"  平均时间: {e2e['avg_time_ms']:.2f}ms")
            print(f"  P95时间: {e2e['p95_time_ms']:.2f}ms")
            print(f"  P99时间: {e2e['p99_time_ms']:.2f}ms")
            print(f"  目标达成: {'✅' if e2e['target_met'] else '❌'}")
        
        # 内存使用结果
        if 'memory_usage' in results:
            mem = results['memory_usage']
            if 'error' not in mem:
                print(f"\n内存使用:")
                print(f"  初始内存: {mem['initial_memory_mb']:.1f}MB")
                print(f"  最终内存: {mem['final_memory_mb']:.1f}MB")
                print(f"  内存增长: {mem['memory_increase_mb']:.1f}MB")


def main():
    """主函数"""
    benchmark = PerformanceBenchmark()
    
    # 测试不同配置
    configs = [
        'config/config.yaml',
        'config/performance_config.yaml'
    ]
    
    all_results = {}
    
    for config_path in configs:
        if Path(config_path).exists():
            print(f"\n{'='*60}")
            print(f"测试配置: {config_path}")
            print(f"{'='*60}")
            
            results = benchmark.run_benchmark_suite(config_path)
            benchmark.print_results(results)
            all_results[config_path] = results
        else:
            print(f"配置文件不存在: {config_path}")
    
    # 保存结果
    if DEPENDENCIES_AVAILABLE:
        try:
            import json
            with open('benchmark_results.json', 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)
            print(f"\n基准测试结果已保存到: benchmark_results.json")
        except Exception as e:
            print(f"保存结果失败: {e}")


if __name__ == "__main__":
    main()
