"""
错误处理和恢复机制
提供系统级的错误处理、恢复和监控功能
"""

import time
import traceback
from collections import defaultdict, deque
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from loguru import logger


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorType(Enum):
    """错误类型"""
    CAMERA_ERROR = "camera_error"
    PROCESSING_ERROR = "processing_error"
    NETWORK_ERROR = "network_error"
    MODEL_ERROR = "model_error"
    MEMORY_ERROR = "memory_error"
    PERFORMANCE_ERROR = "performance_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorRecord:
    """错误记录"""
    
    def __init__(self, 
                 error_type: ErrorType,
                 severity: ErrorSeverity,
                 message: str,
                 exception: Optional[Exception] = None,
                 context: Optional[Dict] = None):
        self.error_type = error_type
        self.severity = severity
        self.message = message
        self.exception = exception
        self.context = context or {}
        self.timestamp = time.time()
        self.count = 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_type': self.error_type.value,
            'severity': self.severity.value,
            'message': self.message,
            'exception': str(self.exception) if self.exception else None,
            'context': self.context,
            'timestamp': self.timestamp,
            'count': self.count
        }


class RecoveryAction:
    """恢复动作"""
    
    def __init__(self, 
                 name: str,
                 action: Callable,
                 max_attempts: int = 3,
                 delay: float = 1.0):
        self.name = name
        self.action = action
        self.max_attempts = max_attempts
        self.delay = delay
        self.attempts = 0
        self.last_attempt_time = 0.0
    
    def can_attempt(self) -> bool:
        """检查是否可以尝试恢复"""
        return (self.attempts < self.max_attempts and 
                time.time() - self.last_attempt_time >= self.delay)
    
    def execute(self) -> bool:
        """执行恢复动作"""
        if not self.can_attempt():
            return False
        
        self.attempts += 1
        self.last_attempt_time = time.time()
        
        try:
            result = self.action()
            if result:
                logger.info(f"恢复动作 '{self.name}' 执行成功")
                self.attempts = 0  # 重置计数
            return result
        except Exception as e:
            logger.error(f"恢复动作 '{self.name}' 执行失败: {e}")
            return False
    
    def reset(self):
        """重置恢复动作"""
        self.attempts = 0
        self.last_attempt_time = 0.0


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化错误处理器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        
        # 错误记录
        self.error_history = deque(maxlen=1000)
        self.error_counts = defaultdict(int)
        self.error_rates = defaultdict(lambda: deque(maxlen=100))
        
        # 恢复动作
        self.recovery_actions = {}
        
        # 阈值配置
        self.error_rate_threshold = self.config.get('error_rate_threshold', 10)  # 每分钟错误数
        self.critical_error_threshold = self.config.get('critical_error_threshold', 5)
        
        # 监控配置
        self.monitoring_enabled = self.config.get('monitoring_enabled', True)
        self.auto_recovery_enabled = self.config.get('auto_recovery_enabled', True)
        
        logger.info("错误处理器初始化完成")
    
    def handle_error(self, 
                    error_type: ErrorType,
                    severity: ErrorSeverity,
                    message: str,
                    exception: Optional[Exception] = None,
                    context: Optional[Dict] = None,
                    auto_recover: bool = True) -> bool:
        """
        处理错误
        
        Args:
            error_type: 错误类型
            severity: 严重程度
            message: 错误消息
            exception: 异常对象
            context: 上下文信息
            auto_recover: 是否自动恢复
            
        Returns:
            是否成功处理/恢复
        """
        # 创建错误记录
        error_record = ErrorRecord(error_type, severity, message, exception, context)
        
        # 检查是否是重复错误
        existing_error = self._find_similar_error(error_record)
        if existing_error:
            existing_error.count += 1
            existing_error.timestamp = time.time()
        else:
            self.error_history.append(error_record)
        
        # 更新统计
        self._update_error_statistics(error_type, severity)
        
        # 记录日志
        self._log_error(error_record)
        
        # 检查是否需要告警
        self._check_alert_conditions(error_type, severity)
        
        # 尝试自动恢复
        if auto_recover and self.auto_recovery_enabled:
            return self._attempt_recovery(error_type, error_record)
        
        return False
    
    def register_recovery_action(self, 
                                error_type: ErrorType,
                                name: str,
                                action: Callable,
                                max_attempts: int = 3,
                                delay: float = 1.0):
        """
        注册恢复动作
        
        Args:
            error_type: 错误类型
            name: 动作名称
            action: 恢复函数
            max_attempts: 最大尝试次数
            delay: 重试延迟
        """
        if error_type not in self.recovery_actions:
            self.recovery_actions[error_type] = []
        
        recovery_action = RecoveryAction(name, action, max_attempts, delay)
        self.recovery_actions[error_type].append(recovery_action)
        
        logger.info(f"注册恢复动作: {error_type.value} -> {name}")
    
    def _find_similar_error(self, error_record: ErrorRecord) -> Optional[ErrorRecord]:
        """查找相似的错误记录"""
        for existing_error in reversed(self.error_history):
            if (existing_error.error_type == error_record.error_type and
                existing_error.message == error_record.message and
                time.time() - existing_error.timestamp < 60):  # 1分钟内的相似错误
                return existing_error
        return None
    
    def _update_error_statistics(self, error_type: ErrorType, severity: ErrorSeverity):
        """更新错误统计"""
        self.error_counts[error_type] += 1
        self.error_rates[error_type].append(time.time())
        
        # 清理过期的错误率记录（超过1分钟）
        current_time = time.time()
        while (self.error_rates[error_type] and 
               current_time - self.error_rates[error_type][0] > 60):
            self.error_rates[error_type].popleft()
    
    def _log_error(self, error_record: ErrorRecord):
        """记录错误日志"""
        log_message = f"[{error_record.error_type.value}] {error_record.message}"
        
        if error_record.context:
            log_message += f" | 上下文: {error_record.context}"
        
        if error_record.exception:
            log_message += f" | 异常: {error_record.exception}"
        
        if error_record.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_record.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_record.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
        
        # 如果有异常，记录完整的堆栈跟踪
        if error_record.exception:
            logger.debug(f"堆栈跟踪:\n{traceback.format_exc()}")
    
    def _check_alert_conditions(self, error_type: ErrorType, severity: ErrorSeverity):
        """检查告警条件"""
        # 检查错误率
        error_rate = len(self.error_rates[error_type])
        if error_rate >= self.error_rate_threshold:
            logger.warning(f"错误率过高: {error_type.value} - {error_rate}/分钟")
        
        # 检查严重错误
        if severity == ErrorSeverity.CRITICAL:
            critical_count = sum(1 for record in self.error_history 
                               if (record.severity == ErrorSeverity.CRITICAL and
                                   time.time() - record.timestamp < 300))  # 5分钟内
            
            if critical_count >= self.critical_error_threshold:
                logger.critical(f"严重错误过多: {critical_count} 个严重错误在5分钟内")
    
    def _attempt_recovery(self, error_type: ErrorType, error_record: ErrorRecord) -> bool:
        """尝试自动恢复"""
        if error_type not in self.recovery_actions:
            return False
        
        logger.info(f"尝试自动恢复: {error_type.value}")
        
        for recovery_action in self.recovery_actions[error_type]:
            if recovery_action.execute():
                logger.info(f"自动恢复成功: {recovery_action.name}")
                return True
        
        logger.warning(f"自动恢复失败: {error_type.value}")
        return False
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        stats = {
            'total_errors': len(self.error_history),
            'error_counts_by_type': dict(self.error_counts),
            'error_rates_by_type': {},
            'recent_errors': [],
            'recovery_status': {}
        }
        
        # 计算错误率
        for error_type, timestamps in self.error_rates.items():
            stats['error_rates_by_type'][error_type.value] = len(timestamps)
        
        # 最近的错误
        recent_errors = list(self.error_history)[-10:]  # 最近10个错误
        stats['recent_errors'] = [error.to_dict() for error in recent_errors]
        
        # 恢复状态
        for error_type, actions in self.recovery_actions.items():
            stats['recovery_status'][error_type.value] = [
                {
                    'name': action.name,
                    'attempts': action.attempts,
                    'max_attempts': action.max_attempts,
                    'can_attempt': action.can_attempt()
                }
                for action in actions
            ]
        
        return stats
    
    def reset_recovery_actions(self, error_type: Optional[ErrorType] = None):
        """重置恢复动作"""
        if error_type:
            if error_type in self.recovery_actions:
                for action in self.recovery_actions[error_type]:
                    action.reset()
                logger.info(f"重置恢复动作: {error_type.value}")
        else:
            for actions in self.recovery_actions.values():
                for action in actions:
                    action.reset()
            logger.info("重置所有恢复动作")
    
    def clear_error_history(self):
        """清空错误历史"""
        self.error_history.clear()
        self.error_counts.clear()
        self.error_rates.clear()
        logger.info("错误历史已清空")
    
    def set_monitoring_enabled(self, enabled: bool):
        """设置监控启用状态"""
        self.monitoring_enabled = enabled
        logger.info(f"错误监控{'启用' if enabled else '禁用'}")
    
    def set_auto_recovery_enabled(self, enabled: bool):
        """设置自动恢复启用状态"""
        self.auto_recovery_enabled = enabled
        logger.info(f"自动恢复{'启用' if enabled else '禁用'}")


def handle_exception(error_handler: ErrorHandler, 
                    error_type: ErrorType = ErrorType.UNKNOWN_ERROR,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    context: Optional[Dict] = None):
    """
    异常处理装饰器
    
    Args:
        error_handler: 错误处理器实例
        error_type: 错误类型
        severity: 严重程度
        context: 上下文信息
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.handle_error(
                    error_type=error_type,
                    severity=severity,
                    message=f"函数 {func.__name__} 执行失败",
                    exception=e,
                    context=context
                )
                raise
        return wrapper
    return decorator


class SystemHealthMonitor:
    """系统健康监控器"""
    
    def __init__(self, error_handler: ErrorHandler):
        """
        初始化健康监控器
        
        Args:
            error_handler: 错误处理器
        """
        self.error_handler = error_handler
        self.health_checks = {}
        self.last_check_time = {}
        
        logger.info("系统健康监控器初始化完成")
    
    def register_health_check(self, 
                             name: str,
                             check_func: Callable[[], bool],
                             interval: float = 30.0,
                             error_type: ErrorType = ErrorType.UNKNOWN_ERROR):
        """
        注册健康检查
        
        Args:
            name: 检查名称
            check_func: 检查函数
            interval: 检查间隔（秒）
            error_type: 错误类型
        """
        self.health_checks[name] = {
            'func': check_func,
            'interval': interval,
            'error_type': error_type
        }
        self.last_check_time[name] = 0.0
        
        logger.info(f"注册健康检查: {name}")
    
    def run_health_checks(self) -> Dict[str, bool]:
        """运行健康检查"""
        results = {}
        current_time = time.time()
        
        for name, check_config in self.health_checks.items():
            # 检查是否到了检查时间
            if current_time - self.last_check_time[name] < check_config['interval']:
                continue
            
            try:
                result = check_config['func']()
                results[name] = result
                
                if not result:
                    self.error_handler.handle_error(
                        error_type=check_config['error_type'],
                        severity=ErrorSeverity.MEDIUM,
                        message=f"健康检查失败: {name}",
                        context={'check_name': name}
                    )
                
                self.last_check_time[name] = current_time
                
            except Exception as e:
                results[name] = False
                self.error_handler.handle_error(
                    error_type=check_config['error_type'],
                    severity=ErrorSeverity.HIGH,
                    message=f"健康检查异常: {name}",
                    exception=e,
                    context={'check_name': name}
                )
        
        return results
