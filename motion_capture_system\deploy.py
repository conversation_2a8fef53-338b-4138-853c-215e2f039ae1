#!/usr/bin/env python3
"""
实时动作捕捉和生成系统 - 部署脚本
用于打包和分发系统
"""

import os
import sys
import shutil
import zipfile
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

class MotionCaptureDeployer:
    """动作捕捉系统部署器"""
    
    def __init__(self):
        """初始化部署器"""
        self.project_root = Path(__file__).parent
        self.version = self._get_version()
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        
    def _get_version(self) -> str:
        """获取版本号"""
        # 尝试从git获取版本
        try:
            import subprocess
            result = subprocess.run(
                ["git", "describe", "--tags", "--always"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
        
        # 默认版本
        return "v1.0.0"
    
    def clean_build_dirs(self):
        """清理构建目录"""
        print("🧹 清理构建目录...")
        
        for dir_path in [self.build_dir, self.dist_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"  删除: {dir_path}")
            
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"  创建: {dir_path}")
    
    def copy_source_files(self):
        """复制源文件"""
        print("📁 复制源文件...")
        
        # 要包含的文件和目录
        include_patterns = [
            "src/",
            "config/",
            "examples/",
            "tests/",
            "*.py",
            "*.md",
            "*.txt",
            "*.yaml",
            "*.yml"
        ]
        
        # 要排除的文件和目录
        exclude_patterns = [
            "__pycache__/",
            "*.pyc",
            ".git/",
            ".gitignore",
            "venv/",
            "motion_env/",
            "build/",
            "dist/",
            "logs/",
            "data/",
            "models/",
            "temp/",
            ".pytest_cache/",
            "*.log"
        ]
        
        build_src = self.build_dir / "motion_capture_system"
        build_src.mkdir(parents=True, exist_ok=True)
        
        def should_exclude(path: Path) -> bool:
            """检查是否应该排除文件"""
            path_str = str(path.relative_to(self.project_root))
            
            for pattern in exclude_patterns:
                if pattern.endswith('/'):
                    if path_str.startswith(pattern[:-1]):
                        return True
                elif pattern.startswith('*.'):
                    if path_str.endswith(pattern[1:]):
                        return True
                elif pattern in path_str:
                    return True
            
            return False
        
        # 复制文件
        copied_files = 0
        for item in self.project_root.rglob("*"):
            if item.is_file() and not should_exclude(item):
                rel_path = item.relative_to(self.project_root)
                dest_path = build_src / rel_path
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(item, dest_path)
                copied_files += 1
        
        print(f"  复制了 {copied_files} 个文件")
        return build_src
    
    def create_deployment_info(self, build_src: Path):
        """创建部署信息文件"""
        print("📋 创建部署信息...")
        
        deployment_info = {
            "name": "实时动作捕捉和生成系统",
            "version": self.version,
            "build_date": datetime.now().isoformat(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "platform": sys.platform,
            "description": "基于MediaPipe和深度学习的实时人体动作捕捉、预测和生成系统",
            "author": "Motion Capture Team",
            "license": "MIT",
            "homepage": "https://github.com/your-username/motion-capture-system",
            "requirements": {
                "python": ">=3.8",
                "memory": ">=4GB",
                "camera": "USB摄像头",
                "network": "用于OSC通信"
            },
            "features": [
                "实时姿态检测 (MediaPipe)",
                "动作预测和生成 (LSTM/Transformer)",
                "OSC通信 (TouchDesigner兼容)",
                "多种滤波算法",
                "性能监控",
                "自动优化配置"
            ],
            "installation": {
                "steps": [
                    "解压文件到目标目录",
                    "运行 python install.py",
                    "运行 python system_optimizer.py",
                    "启动 python main.py"
                ]
            }
        }
        
        info_file = build_src / "deployment_info.json"
        with open(info_file, "w", encoding="utf-8") as f:
            json.dump(deployment_info, f, indent=2, ensure_ascii=False)
        
        print(f"  创建: {info_file}")
    
    def create_quick_start_guide(self, build_src: Path):
        """创建快速开始指南"""
        print("📖 创建快速开始指南...")
        
        guide_content = f"""# 🚀 快速开始指南

## 系统信息
- 版本: {self.version}
- 构建日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🔧 安装步骤

### 1. 系统要求
- Python 3.8 或更高版本
- 4GB 或更多内存
- USB 摄像头
- 网络连接 (用于OSC通信)

### 2. 自动安装
```bash
# 运行自动安装脚本
python install.py
```

### 3. 手动安装 (可选)
```bash
# 创建虚拟环境
python -m venv motion_env

# 激活虚拟环境
# Windows:
motion_env\\Scripts\\activate
# Linux/Mac:
source motion_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

## 🎬 快速使用

### 1. 系统优化
```bash
# 自动生成最优配置
python system_optimizer.py
```

### 2. 启动系统
```bash
# 使用默认配置
python main.py

# 使用优化配置
python run_system.py --config config/optimized_ultra_low_latency.yaml
```

### 3. 运行演示
```bash
# 交互式演示程序
python demo.py
```

### 4. 性能测试
```bash
# 运行性能基准测试
python benchmark_performance.py
```

## 🎨 TouchDesigner 集成

### OSC 设置
1. 在 TouchDesigner 中添加 `OSC In CHOP`
2. 设置网络地址: `127.0.0.1`
3. 设置端口: `9001`
4. 启用 `Active`

### 数据格式
系统发送以下 OSC 消息:
- `/pose/joint_N/x,y,z` - 关键点位置
- `/pose/center/x,y,z` - 身体质心
- `/pose/meta/fps` - 当前帧率
- `/control/beat` - 节拍信号

详细信息请参考 `examples/TouchDesigner_Setup_Guide.md`

## 🔧 配置说明

### 配置文件
- `config/config.yaml` - 默认配置
- `config/optimized_ultra_low_latency.yaml` - 超低延迟配置
- `config/performance_config.yaml` - 高性能配置

### 主要参数
```yaml
camera:
  device_id: 0      # 摄像头设备ID
  fps: 30          # 帧率
  width: 640       # 分辨率宽度
  height: 480      # 分辨率高度

osc:
  enabled: true    # 启用OSC通信
  host: "127.0.0.1" # OSC服务器地址
  port: 9001       # OSC端口

performance:
  target_latency_ms: 50  # 目标延迟
  enable_frame_skip: false # 启用帧跳过
```

## 🐛 故障排除

### 常见问题

1. **摄像头无法打开**
   - 检查摄像头连接
   - 确认设备ID正确
   - 关闭其他使用摄像头的程序

2. **OSC连接失败**
   - 检查端口是否被占用
   - 确认防火墙设置
   - 验证TouchDesigner设置

3. **性能问题**
   - 使用低延迟配置
   - 降低分辨率和帧率
   - 关闭不必要的功能

### 调试模式
```bash
# 启用调试模式
python main.py --debug --log-level DEBUG

# 显示实时视频
python main.py --debug --show-video
```

## 📚 更多资源

- 完整文档: `README.md`
- API文档: `API_DOCUMENTATION.md`
- 示例代码: `examples/` 目录
- 测试用例: `tests/` 目录

## 📞 支持

如有问题，请查看:
- GitHub Issues: https://github.com/your-repo/issues
- 文档: README.md
- 日志文件: logs/ 目录

---

🎭 **祝您使用愉快！**
"""
        
        guide_file = build_src / "QUICK_START.md"
        with open(guide_file, "w", encoding="utf-8") as f:
            f.write(guide_content)
        
        print(f"  创建: {guide_file}")
    
    def create_batch_installer(self, build_src: Path):
        """创建批处理安装器 (Windows)"""
        print("🔧 创建Windows批处理安装器...")
        
        batch_content = f"""@echo off
chcp 65001 >nul
title 实时动作捕捉和生成系统 - 安装器 {self.version}

echo.
echo ================================================================
echo 🎭 实时动作捕捉和生成系统 - 自动安装器
echo ================================================================
echo 版本: {self.version}
echo 构建日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
echo.

echo 📋 开始安装...
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python检查通过

REM 运行安装脚本
echo.
echo 📦 运行安装脚本...
python install.py

if errorlevel 1 (
    echo.
    echo ❌ 安装失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo ✅ 安装完成！
echo.
echo 🚀 下一步操作:
echo 1. 运行系统优化器: python system_optimizer.py
echo 2. 启动主程序: python main.py
echo 3. 运行演示程序: python demo.py
echo.
echo 📖 更多信息请查看 QUICK_START.md 和 README.md
echo.

pause
"""
        
        installer_file = build_src / "install_windows.bat"
        with open(installer_file, "w", encoding="utf-8") as f:
            f.write(batch_content)
        
        print(f"  创建: {installer_file}")
    
    def create_shell_installer(self, build_src: Path):
        """创建Shell安装器 (Linux/Mac)"""
        print("🔧 创建Shell安装器...")
        
        shell_content = f"""#!/bin/bash

# 实时动作捕捉和生成系统 - 安装器 {self.version}

echo "================================================================"
echo "🎭 实时动作捕捉和生成系统 - 自动安装器"
echo "================================================================"
echo "版本: {self.version}"
echo "构建日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
echo ""

echo "📋 开始安装..."
echo ""

# 检查Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ 错误: 未找到Python，请先安装Python 3.8或更高版本"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python检查通过"

# 运行安装脚本
echo ""
echo "📦 运行安装脚本..."
$PYTHON_CMD install.py

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ 安装失败，请检查错误信息"
    exit 1
fi

echo ""
echo "✅ 安装完成！"
echo ""
echo "🚀 下一步操作:"
echo "1. 运行系统优化器: $PYTHON_CMD system_optimizer.py"
echo "2. 启动主程序: $PYTHON_CMD main.py"
echo "3. 运行演示程序: $PYTHON_CMD demo.py"
echo ""
echo "📖 更多信息请查看 QUICK_START.md 和 README.md"
echo ""
"""
        
        installer_file = build_src / "install_unix.sh"
        with open(installer_file, "w", encoding="utf-8") as f:
            f.write(shell_content)
        
        # 设置执行权限
        os.chmod(installer_file, 0o755)
        
        print(f"  创建: {installer_file}")
    
    def create_zip_package(self, build_src: Path):
        """创建ZIP压缩包"""
        print("📦 创建ZIP压缩包...")
        
        zip_name = f"motion_capture_system_{self.version}.zip"
        zip_path = self.dist_dir / zip_name
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in build_src.rglob("*"):
                if file_path.is_file():
                    arc_name = file_path.relative_to(build_src.parent)
                    zipf.write(file_path, arc_name)
        
        print(f"  创建: {zip_path}")
        print(f"  大小: {zip_path.stat().st_size / 1024 / 1024:.1f} MB")
        
        return zip_path
    
    def generate_deployment_summary(self, zip_path: Path):
        """生成部署摘要"""
        print("📊 生成部署摘要...")
        
        summary = f"""# 部署摘要

## 构建信息
- 版本: {self.version}
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}
- 平台: {sys.platform}

## 包信息
- 文件名: {zip_path.name}
- 大小: {zip_path.stat().st_size / 1024 / 1024:.1f} MB
- 路径: {zip_path}

## 安装说明
1. 解压ZIP文件到目标目录
2. Windows用户: 双击 install_windows.bat
3. Linux/Mac用户: 运行 ./install_unix.sh
4. 或手动运行: python install.py

## 快速开始
1. python system_optimizer.py  # 系统优化
2. python main.py             # 启动系统
3. python demo.py             # 运行演示

## 文件结构
motion_capture_system/
├── src/                      # 源代码
├── config/                   # 配置文件
├── examples/                 # 示例代码
├── tests/                    # 测试文件
├── install.py               # 安装脚本
├── install_windows.bat      # Windows安装器
├── install_unix.sh          # Unix安装器
├── QUICK_START.md           # 快速开始指南
├── README.md                # 完整文档
└── deployment_info.json     # 部署信息

## 支持
- GitHub: https://github.com/your-username/motion-capture-system
- Issues: https://github.com/your-username/motion-capture-system/issues
- 文档: README.md
"""
        
        summary_file = self.dist_dir / f"deployment_summary_{self.version}.md"
        with open(summary_file, "w", encoding="utf-8") as f:
            f.write(summary)
        
        print(f"  创建: {summary_file}")
    
    def deploy(self):
        """执行完整部署流程"""
        print(f"🚀 开始部署 Motion Capture System {self.version}")
        print("=" * 60)
        
        try:
            # 1. 清理构建目录
            self.clean_build_dirs()
            
            # 2. 复制源文件
            build_src = self.copy_source_files()
            
            # 3. 创建部署信息
            self.create_deployment_info(build_src)
            
            # 4. 创建快速开始指南
            self.create_quick_start_guide(build_src)
            
            # 5. 创建安装器
            self.create_batch_installer(build_src)
            self.create_shell_installer(build_src)
            
            # 6. 创建ZIP包
            zip_path = self.create_zip_package(build_src)
            
            # 7. 生成部署摘要
            self.generate_deployment_summary(zip_path)
            
            print("\n" + "=" * 60)
            print("🎉 部署完成!")
            print("=" * 60)
            print(f"📦 包文件: {zip_path}")
            print(f"📊 摘要: {self.dist_dir / f'deployment_summary_{self.version}.md'}")
            print("\n🚀 分发说明:")
            print("1. 将ZIP文件分发给用户")
            print("2. 用户解压后运行相应的安装器")
            print("3. 或者用户手动运行 python install.py")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 部署失败: {e}")
            return False


def main():
    """主函数"""
    try:
        deployer = MotionCaptureDeployer()
        success = deployer.deploy()
        
        if success:
            print("\n✅ 部署成功完成!")
            sys.exit(0)
        else:
            print("\n❌ 部署失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  部署被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 部署过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
