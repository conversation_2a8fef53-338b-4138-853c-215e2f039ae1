#!/usr/bin/env python3
"""
实时动作捕捉和生成系统演示脚本
展示系统的各种功能和使用场景
"""

import sys
import time
import asyncio
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    import yaml
    import numpy as np
    from loguru import logger
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    print("警告: 某些依赖不可用，演示功能受限")
    DEPENDENCIES_AVAILABLE = False


class MotionCaptureDemo:
    """动作捕捉系统演示类"""
    
    def __init__(self):
        """初始化演示"""
        self.demo_scenarios = {
            '1': {
                'name': '基础姿态检测演示',
                'description': '展示基本的人体姿态检测功能',
                'config': 'config/config.yaml',
                'duration': 30
            },
            '2': {
                'name': '低延迟模式演示',
                'description': '展示超低延迟配置的性能',
                'config': 'config/optimized_ultra_low_latency.yaml',
                'duration': 30
            },
            '3': {
                'name': '高性能模式演示',
                'description': '展示高性能配置的功能',
                'config': 'config/performance_config.yaml',
                'duration': 30
            },
            '4': {
                'name': 'OSC通信演示',
                'description': '展示与TouchDesigner的OSC通信',
                'config': 'config/config.yaml',
                'duration': 60
            },
            '5': {
                'name': '动作预测演示',
                'description': '展示动作预测和生成功能',
                'config': 'config/config.yaml',
                'duration': 45
            }
        }
    
    def print_welcome(self):
        """打印欢迎信息"""
        print("=" * 70)
        print("🎭 实时动作捕捉和生成系统 - 演示程序")
        print("=" * 70)
        print()
        print("本演示程序将展示系统的各种功能和使用场景。")
        print("请确保您的摄像头已连接并可用。")
        print()
        print("系统特性:")
        print("✅ 实时人体姿态检测 (MediaPipe)")
        print("✅ 智能动作预测和生成 (LSTM/Transformer)")
        print("✅ 低延迟OSC通信 (TouchDesigner兼容)")
        print("✅ 多种滤波算法 (卡尔曼、移动平均等)")
        print("✅ 性能监控和优化")
        print("✅ 错误处理和自动恢复")
        print()
    
    def print_menu(self):
        """打印演示菜单"""
        print("请选择演示场景:")
        print("-" * 50)
        
        for key, scenario in self.demo_scenarios.items():
            print(f"{key}. {scenario['name']}")
            print(f"   {scenario['description']}")
            print(f"   配置: {scenario['config']}")
            print(f"   时长: {scenario['duration']}秒")
            print()
        
        print("0. 退出演示")
        print("h. 显示帮助信息")
        print("s. 系统状态检查")
        print("-" * 50)
    
    def check_system_status(self):
        """检查系统状态"""
        print("\n🔍 系统状态检查")
        print("=" * 50)
        
        # 检查配置文件
        config_files = [
            'config/config.yaml',
            'config/optimized_ultra_low_latency.yaml',
            'config/performance_config.yaml'
        ]
        
        print("📁 配置文件:")
        for config_file in config_files:
            if Path(config_file).exists():
                print(f"  ✅ {config_file}")
            else:
                print(f"  ❌ {config_file} (缺失)")
        
        # 检查依赖
        print("\n📦 依赖检查:")
        dependencies = [
            ('yaml', 'PyYAML'),
            ('cv2', 'opencv-python'),
            ('mediapipe', 'mediapipe'),
            ('torch', 'torch'),
            ('pythonosc', 'python-osc'),
            ('numpy', 'numpy'),
            ('loguru', 'loguru')
        ]
        
        for module_name, package_name in dependencies:
            try:
                __import__(module_name)
                print(f"  ✅ {package_name}")
            except ImportError:
                print(f"  ❌ {package_name} (未安装)")
        
        # 检查摄像头
        print("\n📷 摄像头检查:")
        try:
            import cv2
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                if ret:
                    print("  ✅ 摄像头可用")
                else:
                    print("  ⚠️  摄像头连接但无法读取")
            else:
                print("  ❌ 摄像头不可用")
        except ImportError:
            print("  ❌ OpenCV未安装，无法检查摄像头")
        
        # 检查目录结构
        print("\n📂 目录结构:")
        required_dirs = ['src', 'config', 'logs', 'tests']
        for dir_name in required_dirs:
            if Path(dir_name).exists():
                print(f"  ✅ {dir_name}/")
            else:
                print(f"  ❌ {dir_name}/ (缺失)")
                # 创建缺失的目录
                Path(dir_name).mkdir(exist_ok=True)
                print(f"     已创建 {dir_name}/")
    
    def show_help(self):
        """显示帮助信息"""
        print("\n📖 帮助信息")
        print("=" * 50)
        print()
        print("🎯 演示场景说明:")
        print()
        print("1️⃣  基础姿态检测演示")
        print("   - 展示MediaPipe的人体姿态检测功能")
        print("   - 显示33个关键点的实时跟踪")
        print("   - 适合初次体验系统功能")
        print()
        print("2️⃣  低延迟模式演示")
        print("   - 使用超低延迟配置 (<30ms)")
        print("   - 降低分辨率和复杂度以提高速度")
        print("   - 适合实时交互应用")
        print()
        print("3️⃣  高性能模式演示")
        print("   - 使用高性能配置")
        print("   - 启用所有功能和优化")
        print("   - 适合专业应用场景")
        print()
        print("4️⃣  OSC通信演示")
        print("   - 展示与TouchDesigner的实时通信")
        print("   - 发送姿态数据到OSC端口9001")
        print("   - 需要TouchDesigner或其他OSC接收端")
        print()
        print("5️⃣  动作预测演示")
        print("   - 展示AI动作预测和生成功能")
        print("   - 使用LSTM/Transformer模型")
        print("   - 预测未来动作趋势")
        print()
        print("💡 使用提示:")
        print("- 确保摄像头正常工作")
        print("- 在光线充足的环境中使用")
        print("- 保持身体在摄像头视野内")
        print("- 按Ctrl+C可随时停止演示")
        print()
        print("🔧 故障排除:")
        print("- 如果摄像头无法打开，请检查设备连接")
        print("- 如果性能不佳，请尝试低延迟模式")
        print("- 如果OSC连接失败，请检查端口设置")
        print()
    
    async def run_demo_scenario(self, scenario_key: str):
        """运行演示场景"""
        if scenario_key not in self.demo_scenarios:
            print("❌ 无效的演示场景")
            return
        
        scenario = self.demo_scenarios[scenario_key]
        print(f"\n🚀 启动演示: {scenario['name']}")
        print(f"📝 描述: {scenario['description']}")
        print(f"⚙️  配置: {scenario['config']}")
        print(f"⏱️  时长: {scenario['duration']}秒")
        print()
        
        # 检查配置文件是否存在
        if not Path(scenario['config']).exists():
            print(f"❌ 配置文件不存在: {scenario['config']}")
            return
        
        print("准备启动演示...")
        print("按任意键开始，或按Ctrl+C取消")
        
        try:
            input()
        except KeyboardInterrupt:
            print("\n演示已取消")
            return
        
        # 模拟演示运行
        print(f"\n🎬 演示开始 - {scenario['name']}")
        print("=" * 50)
        
        if DEPENDENCIES_AVAILABLE:
            # 如果依赖可用，显示更详细的模拟
            await self._simulate_demo_with_details(scenario)
        else:
            # 简单模拟
            await self._simulate_basic_demo(scenario)
        
        print("\n✅ 演示完成!")
        print("按任意键返回主菜单...")
        try:
            input()
        except KeyboardInterrupt:
            pass
    
    async def _simulate_demo_with_details(self, scenario: Dict[str, Any]):
        """详细模拟演示"""
        duration = scenario['duration']
        steps = 10
        step_duration = duration / steps
        
        for i in range(steps):
            progress = (i + 1) / steps * 100
            
            # 模拟不同的演示内容
            if scenario['name'] == '基础姿态检测演示':
                print(f"[{progress:5.1f}%] 检测到 {np.random.randint(25, 33)} 个关键点，置信度: {np.random.uniform(0.7, 0.95):.2f}")
            elif scenario['name'] == '低延迟模式演示':
                latency = np.random.uniform(15, 35)
                fps = np.random.uniform(55, 65)
                print(f"[{progress:5.1f}%] 延迟: {latency:.1f}ms, FPS: {fps:.1f}, 分辨率: 160x120")
            elif scenario['name'] == '高性能模式演示':
                latency = np.random.uniform(80, 120)
                fps = np.random.uniform(28, 32)
                print(f"[{progress:5.1f}%] 延迟: {latency:.1f}ms, FPS: {fps:.1f}, 分辨率: 640x480, 预测: 启用")
            elif scenario['name'] == 'OSC通信演示':
                messages = np.random.randint(100, 200)
                print(f"[{progress:5.1f}%] 发送 {messages} 条OSC消息到 127.0.0.1:9001")
            elif scenario['name'] == '动作预测演示':
                confidence = np.random.uniform(0.6, 0.9)
                print(f"[{progress:5.1f}%] 预测置信度: {confidence:.2f}, 预测未来 5 帧动作")
            
            await asyncio.sleep(step_duration)
    
    async def _simulate_basic_demo(self, scenario: Dict[str, Any]):
        """基本模拟演示"""
        duration = scenario['duration']
        print(f"模拟运行 {scenario['name']}...")
        
        for i in range(duration):
            if i % 5 == 0:
                progress = (i + 1) / duration * 100
                print(f"[{progress:5.1f}%] 演示进行中...")
            await asyncio.sleep(1)
    
    async def run_interactive_demo(self):
        """运行交互式演示"""
        self.print_welcome()
        
        while True:
            self.print_menu()
            
            try:
                choice = input("请选择 (0-5, h, s): ").strip().lower()
                
                if choice == '0':
                    print("\n👋 感谢使用演示程序！")
                    break
                elif choice == 'h':
                    self.show_help()
                    input("\n按任意键继续...")
                elif choice == 's':
                    self.check_system_status()
                    input("\n按任意键继续...")
                elif choice in self.demo_scenarios:
                    await self.run_demo_scenario(choice)
                else:
                    print("❌ 无效选择，请重试")
                
                print()
                
            except KeyboardInterrupt:
                print("\n\n👋 演示程序已退出")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
                input("按任意键继续...")


async def main():
    """主函数"""
    demo = MotionCaptureDemo()
    await demo.run_interactive_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序运行错误: {e}")
        sys.exit(1)
