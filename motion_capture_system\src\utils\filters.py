"""
数据滤波器
提供多种数据平滑和滤波算法
"""

import time
from collections import deque
from typing import Dict, List, Optional, Any
import numpy as np
from loguru import logger


class KalmanFilter:
    """卡尔曼滤波器"""
    
    def __init__(self, process_noise: float = 0.01, measurement_noise: float = 0.1):
        """
        初始化卡尔曼滤波器
        
        Args:
            process_noise: 过程噪声
            measurement_noise: 测量噪声
        """
        self.process_noise = process_noise
        self.measurement_noise = measurement_noise
        
        # 状态变量
        self.x = None  # 状态估计
        self.P = None  # 误差协方差
        self.Q = process_noise  # 过程噪声协方差
        self.R = measurement_noise  # 测量噪声协方差
        
        self.initialized = False
    
    def update(self, measurement: float) -> float:
        """
        更新滤波器状态
        
        Args:
            measurement: 测量值
            
        Returns:
            滤波后的值
        """
        if not self.initialized:
            # 初始化
            self.x = measurement
            self.P = 1.0
            self.initialized = True
            return measurement
        
        # 预测步骤
        x_pred = self.x
        P_pred = self.P + self.Q
        
        # 更新步骤
        K = P_pred / (P_pred + self.R)  # 卡尔曼增益
        self.x = x_pred + K * (measurement - x_pred)
        self.P = (1 - K) * P_pred
        
        return self.x
    
    def reset(self):
        """重置滤波器"""
        self.x = None
        self.P = None
        self.initialized = False


class MovingAverageFilter:
    """移动平均滤波器"""
    
    def __init__(self, window_size: int = 5):
        """
        初始化移动平均滤波器
        
        Args:
            window_size: 窗口大小
        """
        self.window_size = window_size
        self.buffer = deque(maxlen=window_size)
    
    def update(self, value: float) -> float:
        """
        更新滤波器状态
        
        Args:
            value: 输入值
            
        Returns:
            滤波后的值
        """
        self.buffer.append(value)
        return sum(self.buffer) / len(self.buffer)
    
    def reset(self):
        """重置滤波器"""
        self.buffer.clear()


class ExponentialSmoothingFilter:
    """指数平滑滤波器"""
    
    def __init__(self, alpha: float = 0.3):
        """
        初始化指数平滑滤波器
        
        Args:
            alpha: 平滑系数 (0-1)
        """
        self.alpha = alpha
        self.last_value = None
    
    def update(self, value: float) -> float:
        """
        更新滤波器状态
        
        Args:
            value: 输入值
            
        Returns:
            滤波后的值
        """
        if self.last_value is None:
            self.last_value = value
            return value
        
        self.last_value = self.alpha * value + (1 - self.alpha) * self.last_value
        return self.last_value
    
    def reset(self):
        """重置滤波器"""
        self.last_value = None


class DataFilter:
    """数据滤波器主类"""
    
    def __init__(self, config: Dict):
        """
        初始化数据滤波器
        
        Args:
            config: 滤波配置
        """
        self.config = config
        self.smoothing_config = config.get('smoothing', {})
        self.enabled = self.smoothing_config.get('enabled', True)
        
        if not self.enabled:
            logger.info("数据滤波已禁用")
            return
        
        self.method = self.smoothing_config.get('method', 'kalman')
        self.filters = {}  # 为每个关键点的每个坐标创建独立的滤波器
        
        logger.info(f"数据滤波器初始化完成，使用方法: {self.method}")
    
    def process(self, pose_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理姿态数据
        
        Args:
            pose_data: 输入姿态数据
            
        Returns:
            滤波后的姿态数据
        """
        if not self.enabled or not pose_data or not pose_data.get('has_pose'):
            return pose_data
        
        landmarks = pose_data.get('landmarks')
        if not landmarks:
            return pose_data
        
        # 应用滤波
        filtered_landmarks = self._filter_landmarks(landmarks)
        
        # 更新数据
        filtered_data = pose_data.copy()
        filtered_data['landmarks'] = filtered_landmarks
        filtered_data['filtered'] = True
        filtered_data['filter_method'] = self.method
        
        return filtered_data
    
    def _filter_landmarks(self, landmarks: List[Dict[str, float]]) -> List[Dict[str, float]]:
        """
        滤波关键点数据
        
        Args:
            landmarks: 原始关键点数据
            
        Returns:
            滤波后的关键点数据
        """
        filtered_landmarks = []
        
        for i, landmark in enumerate(landmarks):
            if landmark['visibility'] < 0.5:
                # 低置信度的关键点不进行滤波
                filtered_landmarks.append(landmark)
                continue
            
            filtered_landmark = landmark.copy()
            
            # 为每个坐标应用滤波
            for coord in ['x', 'y', 'z']:
                filter_key = f"{i}_{coord}"
                
                # 创建滤波器（如果不存在）
                if filter_key not in self.filters:
                    self.filters[filter_key] = self._create_filter()
                
                # 应用滤波
                original_value = landmark[coord]
                filtered_value = self.filters[filter_key].update(original_value)
                filtered_landmark[coord] = filtered_value
                
                # 同时更新归一化坐标
                if f'normalized_{coord}' in landmark:
                    norm_filter_key = f"{i}_normalized_{coord}"
                    if norm_filter_key not in self.filters:
                        self.filters[norm_filter_key] = self._create_filter()
                    
                    original_norm_value = landmark[f'normalized_{coord}']
                    filtered_norm_value = self.filters[norm_filter_key].update(original_norm_value)
                    filtered_landmark[f'normalized_{coord}'] = filtered_norm_value
            
            filtered_landmarks.append(filtered_landmark)
        
        return filtered_landmarks
    
    def _create_filter(self):
        """
        创建滤波器实例
        
        Returns:
            滤波器实例
        """
        if self.method == 'kalman':
            return KalmanFilter(
                process_noise=0.01,
                measurement_noise=0.1
            )
        elif self.method == 'moving_average':
            window_size = self.smoothing_config.get('window_size', 5)
            return MovingAverageFilter(window_size)
        elif self.method == 'exponential':
            alpha = self.smoothing_config.get('alpha', 0.3)
            return ExponentialSmoothingFilter(alpha)
        else:
            logger.warning(f"未知的滤波方法: {self.method}，使用卡尔曼滤波")
            return KalmanFilter()
    
    def reset_filters(self):
        """重置所有滤波器"""
        for filter_obj in self.filters.values():
            filter_obj.reset()
        logger.info("所有滤波器已重置")
    
    def get_filter_count(self) -> int:
        """获取滤波器数量"""
        return len(self.filters)
    
    def is_enabled(self) -> bool:
        """检查滤波是否启用"""
        return self.enabled
    
    def set_enabled(self, enabled: bool):
        """设置滤波启用状态"""
        self.enabled = enabled
        if not enabled:
            self.reset_filters()
        logger.info(f"数据滤波{'启用' if enabled else '禁用'}")


class OutlierDetector:
    """异常值检测器"""
    
    def __init__(self, threshold: float = 3.0, window_size: int = 10):
        """
        初始化异常值检测器
        
        Args:
            threshold: 异常值阈值（标准差倍数）
            window_size: 检测窗口大小
        """
        self.threshold = threshold
        self.window_size = window_size
        self.history = deque(maxlen=window_size)
    
    def is_outlier(self, value: float) -> bool:
        """
        检测是否为异常值
        
        Args:
            value: 待检测值
            
        Returns:
            是否为异常值
        """
        if len(self.history) < 3:
            self.history.append(value)
            return False
        
        # 计算历史数据的均值和标准差
        mean = np.mean(self.history)
        std = np.std(self.history)
        
        # 检测异常值
        is_outlier = abs(value - mean) > self.threshold * std
        
        # 如果不是异常值，添加到历史记录
        if not is_outlier:
            self.history.append(value)
        
        return is_outlier
    
    def reset(self):
        """重置检测器"""
        self.history.clear()


class AdaptiveFilter:
    """自适应滤波器"""
    
    def __init__(self, base_alpha: float = 0.3, max_alpha: float = 0.8):
        """
        初始化自适应滤波器
        
        Args:
            base_alpha: 基础平滑系数
            max_alpha: 最大平滑系数
        """
        self.base_alpha = base_alpha
        self.max_alpha = max_alpha
        self.last_value = None
        self.velocity_history = deque(maxlen=5)
    
    def update(self, value: float) -> float:
        """
        更新滤波器状态
        
        Args:
            value: 输入值
            
        Returns:
            滤波后的值
        """
        if self.last_value is None:
            self.last_value = value
            return value
        
        # 计算速度
        velocity = abs(value - self.last_value)
        self.velocity_history.append(velocity)
        
        # 根据运动速度调整平滑系数
        if len(self.velocity_history) >= 3:
            avg_velocity = np.mean(self.velocity_history)
            # 运动越快，平滑系数越大（响应越快）
            alpha = min(self.base_alpha + avg_velocity * 0.1, self.max_alpha)
        else:
            alpha = self.base_alpha
        
        # 应用指数平滑
        self.last_value = alpha * value + (1 - alpha) * self.last_value
        return self.last_value
    
    def reset(self):
        """重置滤波器"""
        self.last_value = None
        self.velocity_history.clear()
