#!/usr/bin/env python3
"""
安装脚本
自动设置虚拟环境和安装依赖
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, check=True, shell=False):
    """运行命令"""
    print(f"执行命令: {command}")
    
    if isinstance(command, str) and not shell:
        command = command.split()
    
    try:
        result = subprocess.run(
            command, 
            check=check, 
            shell=shell,
            capture_output=True, 
            text=True
        )
        
        if result.stdout:
            print(result.stdout)
        
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            raise
        return e


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print("✓ Python版本检查通过")


def create_virtual_environment():
    """创建虚拟环境"""
    venv_path = Path("motion_env")
    
    if venv_path.exists():
        print("虚拟环境已存在，跳过创建")
        return venv_path
    
    print("创建虚拟环境...")
    run_command([sys.executable, "-m", "venv", str(venv_path)])
    
    print("✓ 虚拟环境创建完成")
    return venv_path


def get_activation_command(venv_path):
    """获取虚拟环境激活命令"""
    system = platform.system().lower()
    
    if system == "windows":
        return str(venv_path / "Scripts" / "activate.bat")
    else:
        return f"source {venv_path}/bin/activate"


def get_python_executable(venv_path):
    """获取虚拟环境中的Python可执行文件路径"""
    system = platform.system().lower()
    
    if system == "windows":
        return str(venv_path / "Scripts" / "python.exe")
    else:
        return str(venv_path / "bin" / "python")


def install_dependencies(venv_path):
    """安装依赖包"""
    python_exe = get_python_executable(venv_path)
    
    print("升级pip...")
    run_command([python_exe, "-m", "pip", "install", "--upgrade", "pip"])
    
    print("安装依赖包...")
    run_command([python_exe, "-m", "pip", "install", "-r", "requirements.txt"])
    
    print("✓ 依赖包安装完成")


def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "data",
        "models"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ 创建目录: {directory}")


def create_batch_files(venv_path):
    """创建批处理文件（Windows）"""
    if platform.system().lower() != "windows":
        return
    
    python_exe = get_python_executable(venv_path)
    
    # 创建启动脚本
    start_script = """@echo off
echo 启动实时动作捕捉系统...
call motion_env\\Scripts\\activate.bat
python run_system.py %*
pause
"""
    
    with open("start_system.bat", "w", encoding="utf-8") as f:
        f.write(start_script)
    
    # 创建测试脚本
    test_script = """@echo off
echo 运行模块测试...
call motion_env\\Scripts\\activate.bat
python test_modules.py
pause
"""
    
    with open("test_system.bat", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✓ 创建Windows批处理文件")


def create_shell_scripts(venv_path):
    """创建Shell脚本（Linux/Mac）"""
    if platform.system().lower() == "windows":
        return
    
    # 创建启动脚本
    start_script = """#!/bin/bash
echo "启动实时动作捕捉系统..."
source motion_env/bin/activate
python run_system.py "$@"
"""
    
    with open("start_system.sh", "w") as f:
        f.write(start_script)
    
    os.chmod("start_system.sh", 0o755)
    
    # 创建测试脚本
    test_script = """#!/bin/bash
echo "运行模块测试..."
source motion_env/bin/activate
python test_modules.py
"""
    
    with open("test_system.sh", "w") as f:
        f.write(test_script)
    
    os.chmod("test_system.sh", 0o755)
    
    print("✓ 创建Shell脚本")


def check_camera():
    """检查摄像头可用性"""
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            if ret:
                print("✓ 摄像头检查通过")
                return True
        
        print("⚠️  摄像头不可用，请检查设备连接")
        return False
    except ImportError:
        print("⚠️  OpenCV未安装，无法检查摄像头")
        return False


def print_usage_instructions(venv_path):
    """打印使用说明"""
    system = platform.system().lower()
    activation_cmd = get_activation_command(venv_path)
    
    print("\n" + "="*60)
    print("安装完成！")
    print("="*60)
    
    print("\n使用说明:")
    print("-" * 30)
    
    if system == "windows":
        print("1. 启动系统:")
        print("   双击 start_system.bat")
        print("   或在命令行运行: start_system.bat")
        
        print("\n2. 测试模块:")
        print("   双击 test_system.bat")
        print("   或在命令行运行: test_system.bat")
        
        print("\n3. 手动激活虚拟环境:")
        print(f"   {activation_cmd}")
    else:
        print("1. 启动系统:")
        print("   ./start_system.sh")
        
        print("\n2. 测试模块:")
        print("   ./test_system.sh")
        
        print("\n3. 手动激活虚拟环境:")
        print(f"   {activation_cmd}")
    
    print("\n4. 配置文件:")
    print("   编辑 config/config.yaml 来调整系统参数")
    
    print("\n5. 命令行选项:")
    print("   python run_system.py --help")
    
    print("\n注意事项:")
    print("- 确保摄像头已连接并可用")
    print("- 如果使用OSC，确保TouchDesigner等接收端已启动")
    print("- 首次运行建议先执行模块测试")
    
    print("\n" + "="*60)


def main():
    """主安装函数"""
    print("实时动作捕捉和生成系统 - 安装脚本")
    print("="*50)
    
    # 检查Python版本
    check_python_version()
    
    # 创建虚拟环境
    venv_path = create_virtual_environment()
    
    # 安装依赖
    install_dependencies(venv_path)
    
    # 创建目录
    create_directories()
    
    # 创建启动脚本
    create_batch_files(venv_path)
    create_shell_scripts(venv_path)
    
    # 检查摄像头
    check_camera()
    
    # 打印使用说明
    print_usage_instructions(venv_path)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装失败: {e}")
        sys.exit(1)
