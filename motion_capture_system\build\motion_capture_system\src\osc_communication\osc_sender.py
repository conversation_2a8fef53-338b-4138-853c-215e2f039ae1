"""
OSC发送器
提供与TouchDesigner等软件的实时OSC通信功能
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
import socket
from pythonosc import udp_client
from pythonosc.osc_message_builder import OscMessageBuilder
from loguru import logger


class OSCSender:
    """OSC发送器类"""
    
    def __init__(self, config: Dict):
        """
        初始化OSC发送器
        
        Args:
            config: OSC配置
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.host = config.get('host', '127.0.0.1')
        self.port = config.get('port', 9001)
        
        # 消息格式配置
        self.message_format = config.get('message_format', {})
        self.base_address = self.message_format.get('base_address', '/pose')
        self.include_confidence = self.message_format.get('include_confidence', True)
        self.include_velocity = self.message_format.get('include_velocity', True)
        self.include_acceleration = self.message_format.get('include_acceleration', False)
        
        # 发送配置
        self.send_rate = config.get('send_rate', 30)
        self.batch_size = config.get('batch_size', 33)
        
        # 重试配置
        self.retry_config = config.get('retry', {})
        self.max_attempts = self.retry_config.get('max_attempts', 3)
        self.timeout = self.retry_config.get('timeout', 0.1)
        
        # OSC客户端
        self.client = None
        self.last_send_time = 0.0
        
        # 统计信息
        self.messages_sent = 0
        self.send_errors = 0
        self.total_send_time = 0.0
        
        if self.enabled:
            self._initialize_client()
        
        logger.info(f"OSC发送器初始化完成: {self.host}:{self.port}")
    
    def _initialize_client(self):
        """初始化OSC客户端"""
        try:
            self.client = udp_client.SimpleUDPClient(self.host, self.port)
            
            # 测试连接
            self._test_connection()
            
            logger.info(f"OSC客户端连接成功: {self.host}:{self.port}")
            
        except Exception as e:
            logger.error(f"OSC客户端初始化失败: {e}")
            self.enabled = False
    
    def _test_connection(self):
        """测试OSC连接"""
        try:
            test_address = f"{self.base_address}/test"
            self.client.send_message(test_address, ["connection_test", time.time()])
            logger.debug("OSC连接测试成功")
        except Exception as e:
            logger.warning(f"OSC连接测试失败: {e}")
    
    def send_pose_data(self, pose_data: Dict[str, Any]) -> bool:
        """
        发送姿态数据
        
        Args:
            pose_data: 姿态数据
            
        Returns:
            发送是否成功
        """
        if not self.enabled or not self.client or not pose_data.get('has_pose'):
            return False
        
        # 控制发送频率
        current_time = time.time()
        if current_time - self.last_send_time < 1.0 / self.send_rate:
            return True  # 跳过发送但返回成功
        
        start_time = time.time()
        success = False
        
        try:
            # 发送关键点数据
            if pose_data.get('landmarks'):
                success = self._send_landmarks(pose_data['landmarks'], pose_data)
            
            # 发送元数据
            if success:
                self._send_metadata(pose_data)
            
            # 更新统计信息
            if success:
                self.messages_sent += 1
                self.last_send_time = current_time
                self.total_send_time += time.time() - start_time
            else:
                self.send_errors += 1
            
        except Exception as e:
            logger.error(f"OSC发送失败: {e}")
            self.send_errors += 1
            success = False
        
        return success
    
    async def send_pose_data_async(self, pose_data: Dict[str, Any]) -> bool:
        """异步发送姿态数据"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.send_pose_data, pose_data)
    
    def _send_landmarks(self, landmarks: List[Dict[str, float]], pose_data: Dict[str, Any]) -> bool:
        """
        发送关键点数据
        
        Args:
            landmarks: 关键点列表
            pose_data: 完整姿态数据
            
        Returns:
            发送是否成功
        """
        try:
            # 批量发送关键点
            for i, landmark in enumerate(landmarks):
                if landmark['visibility'] < 0.5:
                    continue  # 跳过低置信度的关键点
                
                # 基础坐标
                joint_address = f"{self.base_address}/joint_{i}"
                
                # 发送坐标
                self.client.send_message(f"{joint_address}/x", landmark['x'])
                self.client.send_message(f"{joint_address}/y", landmark['y'])
                self.client.send_message(f"{joint_address}/z", landmark['z'])
                
                # 发送归一化坐标
                if 'normalized_x' in landmark:
                    self.client.send_message(f"{joint_address}/norm_x", landmark['normalized_x'])
                    self.client.send_message(f"{joint_address}/norm_y", landmark['normalized_y'])
                    self.client.send_message(f"{joint_address}/norm_z", landmark['normalized_z'])
                
                # 发送置信度
                if self.include_confidence:
                    self.client.send_message(f"{joint_address}/confidence", landmark['visibility'])
                
                # 发送关键点名称
                if 'name' in landmark:
                    self.client.send_message(f"{joint_address}/name", landmark['name'])
                
                # 发送预测标志
                if 'predicted' in landmark:
                    self.client.send_message(f"{joint_address}/predicted", landmark['predicted'])
            
            # 发送运动特征
            motion_features = pose_data.get('motion_features')
            if motion_features and (self.include_velocity or self.include_acceleration):
                self._send_motion_features(motion_features)
            
            return True
            
        except Exception as e:
            logger.error(f"关键点数据发送失败: {e}")
            return False
    
    def _send_motion_features(self, motion_features: Dict[str, Any]):
        """
        发送运动特征
        
        Args:
            motion_features: 运动特征数据
        """
        try:
            features_address = f"{self.base_address}/features"
            
            # 发送质心
            center_of_mass = motion_features.get('center_of_mass')
            if center_of_mass:
                self.client.send_message(f"{features_address}/center_x", center_of_mass['x'])
                self.client.send_message(f"{features_address}/center_y", center_of_mass['y'])
                self.client.send_message(f"{features_address}/center_z", center_of_mass['z'])
            
            # 发送边界框
            bounding_box = motion_features.get('bounding_box')
            if bounding_box:
                self.client.send_message(f"{features_address}/bbox_width", bounding_box['width'])
                self.client.send_message(f"{features_address}/bbox_height", bounding_box['height'])
                self.client.send_message(f"{features_address}/bbox_min_x", bounding_box['min_x'])
                self.client.send_message(f"{features_address}/bbox_min_y", bounding_box['min_y'])
                self.client.send_message(f"{features_address}/bbox_max_x", bounding_box['max_x'])
                self.client.send_message(f"{features_address}/bbox_max_y", bounding_box['max_y'])
            
            # 发送姿态置信度
            pose_confidence = motion_features.get('pose_confidence', 0.0)
            self.client.send_message(f"{features_address}/pose_confidence", pose_confidence)
            
            # 发送速度数据
            if self.include_velocity:
                velocities = motion_features.get('velocity')
                if velocities:
                    for vel in velocities:
                        joint_id = vel['id']
                        vel_address = f"{self.base_address}/joint_{joint_id}/velocity"
                        self.client.send_message(f"{vel_address}/vx", vel['vx'])
                        self.client.send_message(f"{vel_address}/vy", vel['vy'])
                        self.client.send_message(f"{vel_address}/vz", vel['vz'])
                        self.client.send_message(f"{vel_address}/speed", vel['speed'])
            
            # 发送加速度数据
            if self.include_acceleration:
                accelerations = motion_features.get('acceleration')
                if accelerations:
                    for acc in accelerations:
                        joint_id = acc['id']
                        acc_address = f"{self.base_address}/joint_{joint_id}/acceleration"
                        self.client.send_message(f"{acc_address}/ax", acc['ax'])
                        self.client.send_message(f"{acc_address}/ay", acc['ay'])
                        self.client.send_message(f"{acc_address}/az", acc['az'])
                        self.client.send_message(f"{acc_address}/acceleration", acc['acceleration'])
            
        except Exception as e:
            logger.error(f"运动特征发送失败: {e}")
    
    def _send_metadata(self, pose_data: Dict[str, Any]):
        """
        发送元数据
        
        Args:
            pose_data: 姿态数据
        """
        try:
            meta_address = f"{self.base_address}/meta"
            
            # 发送时间戳
            self.client.send_message(f"{meta_address}/timestamp", pose_data.get('timestamp', time.time()))
            
            # 发送帧计数
            if 'frame_count' in pose_data:
                self.client.send_message(f"{meta_address}/frame_count", pose_data['frame_count'])
            
            # 发送FPS
            if 'fps' in pose_data:
                self.client.send_message(f"{meta_address}/fps", pose_data['fps'])
            
            # 发送预测信息
            if pose_data.get('prediction_applied'):
                self.client.send_message(f"{meta_address}/prediction_applied", True)
                if 'prediction_confidence' in pose_data:
                    self.client.send_message(f"{meta_address}/prediction_confidence", pose_data['prediction_confidence'])
            
            # 发送滤波信息
            if pose_data.get('filtered'):
                self.client.send_message(f"{meta_address}/filtered", True)
                if 'filter_method' in pose_data:
                    self.client.send_message(f"{meta_address}/filter_method", pose_data['filter_method'])
            
        except Exception as e:
            logger.error(f"元数据发送失败: {e}")
    
    def send_custom_message(self, address: str, *args) -> bool:
        """
        发送自定义OSC消息
        
        Args:
            address: OSC地址
            *args: 消息参数
            
        Returns:
            发送是否成功
        """
        if not self.enabled or not self.client:
            return False
        
        try:
            self.client.send_message(address, list(args))
            return True
        except Exception as e:
            logger.error(f"自定义消息发送失败: {e}")
            return False
    
    def send_batch_messages(self, messages: List[Tuple[str, List]]) -> bool:
        """
        批量发送OSC消息
        
        Args:
            messages: 消息列表，每个元素为(address, args)
            
        Returns:
            发送是否成功
        """
        if not self.enabled or not self.client:
            return False
        
        try:
            for address, args in messages:
                self.client.send_message(address, args)
            return True
        except Exception as e:
            logger.error(f"批量消息发送失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取发送统计信息
        
        Returns:
            统计信息字典
        """
        total_messages = self.messages_sent + self.send_errors
        success_rate = self.messages_sent / total_messages if total_messages > 0 else 0.0
        avg_send_time = self.total_send_time / self.messages_sent if self.messages_sent > 0 else 0.0
        
        return {
            'messages_sent': self.messages_sent,
            'send_errors': self.send_errors,
            'success_rate': success_rate,
            'average_send_time_ms': avg_send_time * 1000,
            'messages_per_second': self.messages_sent / max(self.total_send_time, 0.001),
            'enabled': self.enabled,
            'host': self.host,
            'port': self.port
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.messages_sent = 0
        self.send_errors = 0
        self.total_send_time = 0.0
        self.last_send_time = 0.0
        logger.info("OSC发送统计信息已重置")
    
    def test_connection(self) -> bool:
        """
        测试OSC连接
        
        Returns:
            连接是否正常
        """
        try:
            self._test_connection()
            return True
        except Exception as e:
            logger.error(f"OSC连接测试失败: {e}")
            return False
    
    def set_enabled(self, enabled: bool):
        """
        设置发送器启用状态
        
        Args:
            enabled: 是否启用
        """
        self.enabled = enabled
        if enabled and not self.client:
            self._initialize_client()
        logger.info(f"OSC发送器{'启用' if enabled else '禁用'}")
    
    def cleanup(self):
        """清理资源"""
        if self.client:
            try:
                # 发送断开连接消息
                self.client.send_message(f"{self.base_address}/disconnect", [time.time()])
            except:
                pass
            self.client = None
        
        logger.info("OSC发送器资源已清理")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
