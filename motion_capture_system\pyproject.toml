[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "motion-capture-system"
description = "实时动作捕捉和生成系统"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Motion Capture Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Motion Capture Team", email = "<EMAIL>"}
]
keywords = [
    "motion capture",
    "pose detection", 
    "mediapipe",
    "osc",
    "touchdesigner",
    "real-time",
    "computer vision",
    "machine learning"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Multimedia :: Video :: Capture",
    "Topic :: Software Development :: Libraries :: Python Modules"
]
requires-python = ">=3.8"
dependencies = [
    "pyyaml>=6.0",
    "opencv-python>=4.8.0",
    "numpy>=1.21.0",
    "asyncio-mqtt>=0.11.0",
    "aiofiles>=0.8.0"
]
dynamic = ["version"]

[project.optional-dependencies]
full = [
    "mediapipe>=0.10.0",
    "torch>=1.12.0",
    "python-osc>=1.8.0",
    "loguru>=0.6.0"
]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "pytest-asyncio>=0.18",
    "black>=22.0",
    "flake8>=4.0",
    "mypy>=0.900"
]
gpu = [
    "torch>=1.12.0",
    "torchvision>=0.13.0"
]
visualization = [
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "plotly>=5.0.0"
]
web = [
    "fastapi>=0.75.0",
    "uvicorn>=0.17.0",
    "websockets>=10.0"
]

[project.urls]
Homepage = "https://github.com/your-username/motion-capture-system"
Documentation = "https://github.com/your-username/motion-capture-system/wiki"
Repository = "https://github.com/your-username/motion-capture-system"
"Bug Tracker" = "https://github.com/your-username/motion-capture-system/issues"
Changelog = "https://github.com/your-username/motion-capture-system/blob/main/CHANGELOG.md"

[project.scripts]
motion-capture = "src.main:main"
motion-capture-demo = "demo:main"
motion-capture-optimizer = "system_optimizer:main"
motion-capture-benchmark = "benchmark_performance:main"

[tool.setuptools]
packages = ["src", "src.mdm_generator", "src.mediapipe_capture", "src.osc_communication", "src.utils"]
include-package-data = true

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.md", "*.txt"]

[tool.setuptools_scm]
write_to = "src/_version.py"

# Black代码格式化配置
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | venv
  | motion_env
)/
'''

# isort导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

# MyPy类型检查配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "cv2.*",
    "mediapipe.*",
    "torch.*",
    "pythonosc.*",
    "loguru.*"
]
ignore_missing_imports = true

# Pytest测试配置
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gpu: marks tests that require GPU",
    "camera: marks tests that require camera"
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning"
]

# Coverage配置
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/motion_env/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]

# Flake8配置
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "venv",
    "motion_env",
    ".pytest_cache"
]

# Bandit安全检查配置
[tool.bandit]
exclude_dirs = ["tests", "venv", "motion_env"]
skips = ["B101", "B601"]
