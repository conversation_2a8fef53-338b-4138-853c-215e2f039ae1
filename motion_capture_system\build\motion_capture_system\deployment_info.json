{"name": "实时动作捕捉和生成系统", "version": "v1.0.0", "build_date": "2025-07-22T16:19:40.501361", "python_version": "3.12.9", "platform": "win32", "description": "基于MediaPipe和深度学习的实时人体动作捕捉、预测和生成系统", "author": "Motion Capture Team", "license": "MIT", "homepage": "https://github.com/your-username/motion-capture-system", "requirements": {"python": ">=3.8", "memory": ">=4GB", "camera": "USB摄像头", "network": "用于OSC通信"}, "features": ["实时姿态检测 (MediaPipe)", "动作预测和生成 (LSTM/Transformer)", "OSC通信 (TouchDesigner兼容)", "多种滤波算法", "性能监控", "自动优化配置"], "installation": {"steps": ["解压文件到目标目录", "运行 python install.py", "运行 python system_optimizer.py", "启动 python main.py"]}}