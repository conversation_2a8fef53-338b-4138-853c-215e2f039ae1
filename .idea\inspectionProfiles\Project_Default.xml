<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="32">
            <item index="0" class="java.lang.String" itemvalue="modelscope" />
            <item index="1" class="java.lang.String" itemvalue="shapely" />
            <item index="2" class="java.lang.String" itemvalue="transformers" />
            <item index="3" class="java.lang.String" itemvalue="pydantic" />
            <item index="4" class="java.lang.String" itemvalue="pyzbar" />
            <item index="5" class="java.lang.String" itemvalue="opencv-python" />
            <item index="6" class="java.lang.String" itemvalue="PyYAML" />
            <item index="7" class="java.lang.String" itemvalue="torch" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="requests" />
            <item index="10" class="java.lang.String" itemvalue="torchvision" />
            <item index="11" class="java.lang.String" itemvalue="ultralytics" />
            <item index="12" class="java.lang.String" itemvalue="jinja2" />
            <item index="13" class="java.lang.String" itemvalue="fastapi" />
            <item index="14" class="java.lang.String" itemvalue="matplotlib" />
            <item index="15" class="java.lang.String" itemvalue="pillow" />
            <item index="16" class="java.lang.String" itemvalue="uvicorn" />
            <item index="17" class="java.lang.String" itemvalue="sympy" />
            <item index="18" class="java.lang.String" itemvalue="setuptools" />
            <item index="19" class="java.lang.String" itemvalue="pytest-qt" />
            <item index="20" class="java.lang.String" itemvalue="scipy" />
            <item index="21" class="java.lang.String" itemvalue="pytest" />
            <item index="22" class="java.lang.String" itemvalue="black" />
            <item index="23" class="java.lang.String" itemvalue="plotly" />
            <item index="24" class="java.lang.String" itemvalue="loguru" />
            <item index="25" class="java.lang.String" itemvalue="pandas" />
            <item index="26" class="java.lang.String" itemvalue="pyyaml" />
            <item index="27" class="java.lang.String" itemvalue="matlabengine" />
            <item index="28" class="java.lang.String" itemvalue="threading2" />
            <item index="29" class="java.lang.String" itemvalue="seaborn" />
            <item index="30" class="java.lang.String" itemvalue="PyQt6" />
            <item index="31" class="java.lang.String" itemvalue="PyQt6-tools" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="depth_pro" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>