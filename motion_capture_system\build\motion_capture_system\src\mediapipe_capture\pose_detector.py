"""
MediaPipe姿态检测器
实现实时人体姿态检测和关键点提取
"""

import asyncio
import time
from typing import Dict, List, Optional, Tuple, Any

import cv2
import mediapipe as mp
import numpy as np
from loguru import logger


class PoseDetector:
    """MediaPipe姿态检测器类"""
    
    def __init__(self, camera_config: Dict, mediapipe_config: Dict):
        """
        初始化姿态检测器
        
        Args:
            camera_config: 摄像头配置
            mediapipe_config: MediaPipe配置
        """
        self.camera_config = camera_config
        self.mediapipe_config = mediapipe_config
        
        # 初始化MediaPipe
        self.mp_pose = mp.solutions.pose
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        # 创建姿态检测器
        self.pose = self.mp_pose.Pose(
            model_complexity=mediapipe_config['model_complexity'],
            min_detection_confidence=mediapipe_config['min_detection_confidence'],
            min_tracking_confidence=mediapipe_config['min_tracking_confidence'],
            enable_segmentation=mediapipe_config['enable_segmentation'],
            smooth_landmarks=mediapipe_config['smooth_landmarks']
        )
        
        # 初始化摄像头
        self.cap = None
        self.frame_count = 0
        self.last_fps_time = time.time()
        self.fps = 0.0
        
        # 关键点名称映射
        self.landmark_names = [
            'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',
            'right_eye_inner', 'right_eye', 'right_eye_outer',
            'left_ear', 'right_ear', 'mouth_left', 'mouth_right',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky',
            'left_index', 'right_index', 'left_thumb', 'right_thumb',
            'left_hip', 'right_hip', 'left_knee', 'right_knee',
            'left_ankle', 'right_ankle', 'left_heel', 'right_heel',
            'left_foot_index', 'right_foot_index'
        ]
        
        self._initialize_camera()
        logger.info("MediaPipe姿态检测器初始化完成")
    
    def _initialize_camera(self):
        """初始化摄像头"""
        try:
            self.cap = cv2.VideoCapture(self.camera_config['device_id'])
            
            if not self.cap.isOpened():
                raise RuntimeError(f"无法打开摄像头设备 {self.camera_config['device_id']}")
            
            # 设置摄像头参数
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.camera_config['width'])
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.camera_config['height'])
            self.cap.set(cv2.CAP_PROP_FPS, self.camera_config['fps'])
            
            # 验证设置
            actual_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            logger.info(f"摄像头初始化成功: {actual_width}x{actual_height}@{actual_fps}fps")
            
        except Exception as e:
            logger.error(f"摄像头初始化失败: {e}")
            raise
    
    def get_pose(self) -> Optional[Dict[str, Any]]:
        """
        获取当前帧的姿态数据
        
        Returns:
            包含姿态数据的字典，如果检测失败返回None
        """
        if not self.cap or not self.cap.isOpened():
            return None
        
        # 读取帧
        ret, frame = self.cap.read()
        if not ret:
            logger.warning("无法读取摄像头帧")
            return None
        
        # 转换颜色空间
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 姿态检测
        results = self.pose.process(rgb_frame)
        
        # 更新FPS计算
        self._update_fps()
        
        if results.pose_landmarks:
            # 提取关键点数据
            landmarks_data = self._extract_landmarks(results.pose_landmarks, frame.shape)
            
            # 构建返回数据
            pose_data = {
                'landmarks': landmarks_data,
                'frame_shape': frame.shape,
                'frame_count': self.frame_count,
                'fps': self.fps,
                'timestamp': time.time(),
                'has_pose': True
            }
            
            # 如果启用了分割，添加分割掩码
            if results.pose_world_landmarks:
                world_landmarks = self._extract_world_landmarks(results.pose_world_landmarks)
                pose_data['world_landmarks'] = world_landmarks
            
            return pose_data
        else:
            return {
                'landmarks': None,
                'frame_shape': frame.shape,
                'frame_count': self.frame_count,
                'fps': self.fps,
                'timestamp': time.time(),
                'has_pose': False
            }
    
    async def get_pose_async(self) -> Optional[Dict[str, Any]]:
        """异步获取姿态数据"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.get_pose)
    
    def _extract_landmarks(self, landmarks, frame_shape: Tuple[int, int, int]) -> List[Dict[str, float]]:
        """
        提取关键点数据
        
        Args:
            landmarks: MediaPipe关键点对象
            frame_shape: 帧形状 (height, width, channels)
            
        Returns:
            关键点数据列表
        """
        height, width = frame_shape[:2]
        landmarks_data = []
        
        for i, landmark in enumerate(landmarks.landmark):
            landmark_dict = {
                'id': i,
                'name': self.landmark_names[i] if i < len(self.landmark_names) else f'landmark_{i}',
                'x': landmark.x * width,  # 转换为像素坐标
                'y': landmark.y * height,
                'z': landmark.z * width,  # z坐标相对于x轴缩放
                'visibility': landmark.visibility,
                'normalized_x': landmark.x,  # 保留归一化坐标
                'normalized_y': landmark.y,
                'normalized_z': landmark.z
            }
            landmarks_data.append(landmark_dict)
        
        return landmarks_data
    
    def _extract_world_landmarks(self, world_landmarks) -> List[Dict[str, float]]:
        """
        提取世界坐标系关键点数据
        
        Args:
            world_landmarks: MediaPipe世界坐标关键点对象
            
        Returns:
            世界坐标关键点数据列表
        """
        world_landmarks_data = []
        
        for i, landmark in enumerate(world_landmarks.landmark):
            landmark_dict = {
                'id': i,
                'name': self.landmark_names[i] if i < len(self.landmark_names) else f'landmark_{i}',
                'x': landmark.x,  # 米为单位
                'y': landmark.y,
                'z': landmark.z,
                'visibility': landmark.visibility
            }
            world_landmarks_data.append(landmark_dict)
        
        return world_landmarks_data
    
    def _update_fps(self):
        """更新FPS计算"""
        self.frame_count += 1
        current_time = time.time()
        
        if current_time - self.last_fps_time >= 1.0:  # 每秒更新一次FPS
            self.fps = self.frame_count / (current_time - self.last_fps_time)
            self.frame_count = 0
            self.last_fps_time = current_time
    
    def get_current_frame(self) -> Optional[np.ndarray]:
        """
        获取当前帧图像（用于调试显示）
        
        Returns:
            当前帧图像，如果获取失败返回None
        """
        if not self.cap or not self.cap.isOpened():
            return None
        
        ret, frame = self.cap.read()
        return frame if ret else None
    
    def draw_landmarks(self, frame: np.ndarray, landmarks_data: List[Dict[str, float]]) -> np.ndarray:
        """
        在帧上绘制关键点
        
        Args:
            frame: 输入帧
            landmarks_data: 关键点数据
            
        Returns:
            绘制了关键点的帧
        """
        if not landmarks_data:
            return frame
        
        # 创建MediaPipe格式的关键点对象用于绘制
        landmark_list = []
        for landmark in landmarks_data:
            mp_landmark = self.mp_pose.PoseLandmark()
            mp_landmark.x = landmark['normalized_x']
            mp_landmark.y = landmark['normalized_y']
            mp_landmark.z = landmark['normalized_z']
            mp_landmark.visibility = landmark['visibility']
            landmark_list.append(mp_landmark)
        
        # 创建关键点对象
        pose_landmarks = type('PoseLandmarks', (), {'landmark': landmark_list})()
        
        # 绘制关键点
        annotated_frame = frame.copy()
        self.mp_drawing.draw_landmarks(
            annotated_frame,
            pose_landmarks,
            self.mp_pose.POSE_CONNECTIONS,
            landmark_drawing_spec=self.mp_drawing_styles.get_default_pose_landmarks_style()
        )
        
        return annotated_frame
    
    def get_fps(self) -> float:
        """获取当前FPS"""
        return self.fps
    
    def get_frame_count(self) -> int:
        """获取帧计数"""
        return self.frame_count
    
    def is_camera_opened(self) -> bool:
        """检查摄像头是否正常打开"""
        return self.cap is not None and self.cap.isOpened()
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
            logger.info("摄像头资源已释放")
        
        if hasattr(self, 'pose'):
            self.pose.close()
            logger.info("MediaPipe姿态检测器已关闭")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
