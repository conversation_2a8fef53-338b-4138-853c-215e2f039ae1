camera:
  device_id: 0
  fps: 60
  height: 120
  width: 160
data_processing:
  normalization:
    enabled: false
    method: minmax
  selected_keypoints:
  - 0
  - 11
  - 12
  - 15
  - 16
  - 23
  - 24
  smoothing:
    alpha: 0.7
    enabled: false
    method: exponential
debug:
  save_data: false
  show_landmarks: false
  show_video: false
logging:
  backup_count: 5
  file: logs/motion_capture.log
  level: WARNING
  max_size: 10MB
  performance_monitoring:
    enabled: true
    log_interval: 10
    metrics:
    - fps
    - latency
mdm:
  enabled: false
  model_type: simple
  prediction_length: 1
  sequence_length: 3
mediapipe:
  enable_segmentation: false
  min_detection_confidence: 0.3
  min_tracking_confidence: 0.3
  model_complexity: 0
  smooth_landmarks: false
osc:
  batch_size: 7
  enabled: true
  host: 127.0.0.1
  message_format:
    base_address: /pose
    include_acceleration: false
    include_confidence: false
    include_velocity: false
  port: 9001
  send_rate: 60
performance:
  enable_frame_skip: true
  max_queue_size: 2
  target_latency_ms: 30
  threading:
    capture_thread: true
    max_workers: 1
    osc_thread: true
    processing_thread: true
