"""
MediaPipe数据处理器
提供姿态数据的预处理、标准化和特征提取功能
"""

import time
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from loguru import logger


class DataProcessor:
    """MediaPipe数据处理器类"""
    
    def __init__(self, config: Dict):
        """
        初始化数据处理器
        
        Args:
            config: 数据处理配置
        """
        self.config = config
        self.selected_keypoints = config.get('selected_keypoints', list(range(33)))
        
        # 历史数据缓存（用于计算速度和加速度）
        self.history_buffer = []
        self.max_history_length = 10
        
        logger.info("数据处理器初始化完成")
    
    def process_pose_data(self, pose_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理姿态数据
        
        Args:
            pose_data: 原始姿态数据
            
        Returns:
            处理后的姿态数据
        """
        if not pose_data or not pose_data.get('has_pose'):
            return pose_data
        
        landmarks = pose_data.get('landmarks')
        if not landmarks:
            return pose_data
        
        # 选择指定的关键点
        selected_landmarks = self._select_keypoints(landmarks)
        
        # 数据标准化
        if self.config.get('normalization', {}).get('enabled', True):
            normalized_landmarks = self._normalize_landmarks(selected_landmarks)
        else:
            normalized_landmarks = selected_landmarks
        
        # 计算运动特征
        motion_features = self._calculate_motion_features(normalized_landmarks)
        
        # 更新历史缓存
        self._update_history(normalized_landmarks)
        
        # 构建处理后的数据
        processed_data = pose_data.copy()
        processed_data.update({
            'processed_landmarks': normalized_landmarks,
            'motion_features': motion_features,
            'selected_keypoint_count': len(selected_landmarks),
            'processing_timestamp': time.time()
        })
        
        return processed_data
    
    def _select_keypoints(self, landmarks: List[Dict[str, float]]) -> List[Dict[str, float]]:
        """
        选择指定的关键点
        
        Args:
            landmarks: 所有关键点数据
            
        Returns:
            选择的关键点数据
        """
        selected = []
        for i in self.selected_keypoints:
            if i < len(landmarks):
                selected.append(landmarks[i])
        
        return selected
    
    def _normalize_landmarks(self, landmarks: List[Dict[str, float]]) -> List[Dict[str, float]]:
        """
        标准化关键点数据
        
        Args:
            landmarks: 关键点数据
            
        Returns:
            标准化后的关键点数据
        """
        if not landmarks:
            return landmarks
        
        method = self.config.get('normalization', {}).get('method', 'minmax')
        
        if method == 'minmax':
            return self._minmax_normalize(landmarks)
        elif method == 'zscore':
            return self._zscore_normalize(landmarks)
        else:
            return landmarks
    
    def _minmax_normalize(self, landmarks: List[Dict[str, float]]) -> List[Dict[str, float]]:
        """
        MinMax标准化
        
        Args:
            landmarks: 关键点数据
            
        Returns:
            MinMax标准化后的关键点数据
        """
        if not landmarks:
            return landmarks
        
        # 提取坐标
        x_coords = [lm['x'] for lm in landmarks if lm['visibility'] > 0.5]
        y_coords = [lm['y'] for lm in landmarks if lm['visibility'] > 0.5]
        z_coords = [lm['z'] for lm in landmarks if lm['visibility'] > 0.5]
        
        if not x_coords:
            return landmarks
        
        # 计算范围
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        z_min, z_max = min(z_coords), max(z_coords)
        
        # 避免除零
        x_range = max(x_max - x_min, 1e-6)
        y_range = max(y_max - y_min, 1e-6)
        z_range = max(z_max - z_min, 1e-6)
        
        # 标准化
        normalized_landmarks = []
        for landmark in landmarks:
            normalized_landmark = landmark.copy()
            if landmark['visibility'] > 0.5:
                normalized_landmark['normalized_x'] = (landmark['x'] - x_min) / x_range
                normalized_landmark['normalized_y'] = (landmark['y'] - y_min) / y_range
                normalized_landmark['normalized_z'] = (landmark['z'] - z_min) / z_range
            else:
                normalized_landmark['normalized_x'] = 0.0
                normalized_landmark['normalized_y'] = 0.0
                normalized_landmark['normalized_z'] = 0.0
            
            normalized_landmarks.append(normalized_landmark)
        
        return normalized_landmarks
    
    def _zscore_normalize(self, landmarks: List[Dict[str, float]]) -> List[Dict[str, float]]:
        """
        Z-Score标准化
        
        Args:
            landmarks: 关键点数据
            
        Returns:
            Z-Score标准化后的关键点数据
        """
        if not landmarks:
            return landmarks
        
        # 提取坐标
        x_coords = [lm['x'] for lm in landmarks if lm['visibility'] > 0.5]
        y_coords = [lm['y'] for lm in landmarks if lm['visibility'] > 0.5]
        z_coords = [lm['z'] for lm in landmarks if lm['visibility'] > 0.5]
        
        if not x_coords:
            return landmarks
        
        # 计算均值和标准差
        x_mean, x_std = np.mean(x_coords), np.std(x_coords)
        y_mean, y_std = np.mean(y_coords), np.std(y_coords)
        z_mean, z_std = np.mean(z_coords), np.std(z_coords)
        
        # 避免除零
        x_std = max(x_std, 1e-6)
        y_std = max(y_std, 1e-6)
        z_std = max(z_std, 1e-6)
        
        # 标准化
        normalized_landmarks = []
        for landmark in landmarks:
            normalized_landmark = landmark.copy()
            if landmark['visibility'] > 0.5:
                normalized_landmark['normalized_x'] = (landmark['x'] - x_mean) / x_std
                normalized_landmark['normalized_y'] = (landmark['y'] - y_mean) / y_std
                normalized_landmark['normalized_z'] = (landmark['z'] - z_mean) / z_std
            else:
                normalized_landmark['normalized_x'] = 0.0
                normalized_landmark['normalized_y'] = 0.0
                normalized_landmark['normalized_z'] = 0.0
            
            normalized_landmarks.append(normalized_landmark)
        
        return normalized_landmarks
    
    def _calculate_motion_features(self, landmarks: List[Dict[str, float]]) -> Dict[str, Any]:
        """
        计算运动特征
        
        Args:
            landmarks: 关键点数据
            
        Returns:
            运动特征字典
        """
        features = {
            'velocity': None,
            'acceleration': None,
            'center_of_mass': None,
            'bounding_box': None,
            'pose_confidence': 0.0
        }
        
        if not landmarks:
            return features
        
        # 计算姿态置信度
        visible_landmarks = [lm for lm in landmarks if lm['visibility'] > 0.5]
        if visible_landmarks:
            features['pose_confidence'] = np.mean([lm['visibility'] for lm in visible_landmarks])
        
        # 计算质心
        if visible_landmarks:
            center_x = np.mean([lm['x'] for lm in visible_landmarks])
            center_y = np.mean([lm['y'] for lm in visible_landmarks])
            center_z = np.mean([lm['z'] for lm in visible_landmarks])
            features['center_of_mass'] = {'x': center_x, 'y': center_y, 'z': center_z}
        
        # 计算边界框
        if visible_landmarks:
            x_coords = [lm['x'] for lm in visible_landmarks]
            y_coords = [lm['y'] for lm in visible_landmarks]
            features['bounding_box'] = {
                'min_x': min(x_coords),
                'max_x': max(x_coords),
                'min_y': min(y_coords),
                'max_y': max(y_coords),
                'width': max(x_coords) - min(x_coords),
                'height': max(y_coords) - min(y_coords)
            }
        
        # 计算速度和加速度（需要历史数据）
        if len(self.history_buffer) >= 2:
            features['velocity'] = self._calculate_velocity()
        
        if len(self.history_buffer) >= 3:
            features['acceleration'] = self._calculate_acceleration()
        
        return features
    
    def _calculate_velocity(self) -> Optional[List[Dict[str, float]]]:
        """
        计算关键点速度
        
        Returns:
            速度数据列表
        """
        if len(self.history_buffer) < 2:
            return None
        
        current_frame = self.history_buffer[-1]
        previous_frame = self.history_buffer[-2]
        
        dt = current_frame['timestamp'] - previous_frame['timestamp']
        if dt <= 0:
            return None
        
        velocities = []
        current_landmarks = current_frame['landmarks']
        previous_landmarks = previous_frame['landmarks']
        
        for i, (curr, prev) in enumerate(zip(current_landmarks, previous_landmarks)):
            if curr['visibility'] > 0.5 and prev['visibility'] > 0.5:
                vx = (curr['x'] - prev['x']) / dt
                vy = (curr['y'] - prev['y']) / dt
                vz = (curr['z'] - prev['z']) / dt
                
                velocities.append({
                    'id': i,
                    'vx': vx,
                    'vy': vy,
                    'vz': vz,
                    'speed': np.sqrt(vx**2 + vy**2 + vz**2)
                })
            else:
                velocities.append({
                    'id': i,
                    'vx': 0.0,
                    'vy': 0.0,
                    'vz': 0.0,
                    'speed': 0.0
                })
        
        return velocities
    
    def _calculate_acceleration(self) -> Optional[List[Dict[str, float]]]:
        """
        计算关键点加速度
        
        Returns:
            加速度数据列表
        """
        if len(self.history_buffer) < 3:
            return None
        
        # 计算前两帧的速度
        frame1 = self.history_buffer[-3]
        frame2 = self.history_buffer[-2]
        frame3 = self.history_buffer[-1]
        
        dt1 = frame2['timestamp'] - frame1['timestamp']
        dt2 = frame3['timestamp'] - frame2['timestamp']
        
        if dt1 <= 0 or dt2 <= 0:
            return None
        
        accelerations = []
        
        for i in range(len(frame1['landmarks'])):
            lm1 = frame1['landmarks'][i]
            lm2 = frame2['landmarks'][i]
            lm3 = frame3['landmarks'][i]
            
            if all(lm['visibility'] > 0.5 for lm in [lm1, lm2, lm3]):
                # 计算速度
                v1x = (lm2['x'] - lm1['x']) / dt1
                v1y = (lm2['y'] - lm1['y']) / dt1
                v1z = (lm2['z'] - lm1['z']) / dt1
                
                v2x = (lm3['x'] - lm2['x']) / dt2
                v2y = (lm3['y'] - lm2['y']) / dt2
                v2z = (lm3['z'] - lm2['z']) / dt2
                
                # 计算加速度
                ax = (v2x - v1x) / dt2
                ay = (v2y - v1y) / dt2
                az = (v2z - v1z) / dt2
                
                accelerations.append({
                    'id': i,
                    'ax': ax,
                    'ay': ay,
                    'az': az,
                    'acceleration': np.sqrt(ax**2 + ay**2 + az**2)
                })
            else:
                accelerations.append({
                    'id': i,
                    'ax': 0.0,
                    'ay': 0.0,
                    'az': 0.0,
                    'acceleration': 0.0
                })
        
        return accelerations
    
    def _update_history(self, landmarks: List[Dict[str, float]]):
        """
        更新历史数据缓存
        
        Args:
            landmarks: 当前帧关键点数据
        """
        history_entry = {
            'landmarks': landmarks,
            'timestamp': time.time()
        }
        
        self.history_buffer.append(history_entry)
        
        # 保持缓存大小
        if len(self.history_buffer) > self.max_history_length:
            self.history_buffer.pop(0)
    
    def get_history_length(self) -> int:
        """获取历史缓存长度"""
        return len(self.history_buffer)
    
    def clear_history(self):
        """清空历史缓存"""
        self.history_buffer.clear()
        logger.info("历史数据缓存已清空")
    
    def extract_feature_vector(self, landmarks: List[Dict[str, float]]) -> np.ndarray:
        """
        提取特征向量（用于机器学习模型）
        
        Args:
            landmarks: 关键点数据
            
        Returns:
            特征向量
        """
        if not landmarks:
            return np.zeros(len(self.selected_keypoints) * 3)
        
        features = []
        for landmark in landmarks:
            if landmark['visibility'] > 0.5:
                features.extend([
                    landmark.get('normalized_x', landmark['x']),
                    landmark.get('normalized_y', landmark['y']),
                    landmark.get('normalized_z', landmark['z'])
                ])
            else:
                features.extend([0.0, 0.0, 0.0])
        
        return np.array(features, dtype=np.float32)
