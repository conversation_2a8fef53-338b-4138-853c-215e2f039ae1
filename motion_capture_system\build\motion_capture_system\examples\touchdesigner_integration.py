#!/usr/bin/env python3
"""
TouchDesigner集成示例
展示如何将动作捕捉数据发送到TouchDesigner进行可视化
"""

import sys
import time
import asyncio
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from pythonosc import udp_client
    from pythonosc.osc_message_builder import OscMessageBuilder
    import numpy as np
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    print("警告: OSC依赖不可用，请安装 python-osc")
    DEPENDENCIES_AVAILABLE = False


class TouchDesignerIntegration:
    """TouchDesigner集成类"""
    
    def __init__(self, host: str = "127.0.0.1", port: int = 9001):
        """
        初始化TouchDesigner集成
        
        Args:
            host: TouchDesigner主机地址
            port: OSC端口
        """
        self.host = host
        self.port = port
        self.client = None
        
        if DEPENDENCIES_AVAILABLE:
            try:
                self.client = udp_client.SimpleUDPClient(host, port)
                print(f"✅ OSC客户端已连接: {host}:{port}")
            except Exception as e:
                print(f"❌ OSC客户端连接失败: {e}")
    
    def create_mock_pose_data(self, frame_count: int = 0) -> Dict[str, Any]:
        """创建模拟姿态数据"""
        landmarks = []
        
        # 创建33个关键点的模拟数据
        for i in range(33):
            # 模拟人体运动（简单的正弦波动画）
            base_x = 320 + 100 * np.sin(frame_count * 0.1 + i * 0.2)
            base_y = 240 + 50 * np.cos(frame_count * 0.1 + i * 0.3)
            base_z = 10 * np.sin(frame_count * 0.05 + i * 0.1)
            
            landmark = {
                'id': i,
                'name': f'joint_{i}',
                'x': base_x + np.random.normal(0, 2),
                'y': base_y + np.random.normal(0, 2),
                'z': base_z + np.random.normal(0, 1),
                'visibility': np.random.uniform(0.8, 1.0),
                'normalized_x': (base_x + 320) / 640,
                'normalized_y': (base_y + 240) / 480,
                'normalized_z': base_z / 100
            }
            landmarks.append(landmark)
        
        return {
            'landmarks': landmarks,
            'has_pose': True,
            'timestamp': time.time(),
            'frame_count': frame_count,
            'fps': 30.0
        }
    
    def send_pose_to_touchdesigner(self, pose_data: Dict[str, Any]):
        """发送姿态数据到TouchDesigner"""
        if not self.client or not pose_data.get('has_pose'):
            return
        
        try:
            # 发送关键点数据
            for landmark in pose_data['landmarks']:
                joint_id = landmark['id']
                
                # 发送位置数据
                self.client.send_message(f"/pose/joint_{joint_id}/x", landmark['x'])
                self.client.send_message(f"/pose/joint_{joint_id}/y", landmark['y'])
                self.client.send_message(f"/pose/joint_{joint_id}/z", landmark['z'])
                
                # 发送归一化坐标
                self.client.send_message(f"/pose/joint_{joint_id}/norm_x", landmark['normalized_x'])
                self.client.send_message(f"/pose/joint_{joint_id}/norm_y", landmark['normalized_y'])
                self.client.send_message(f"/pose/joint_{joint_id}/norm_z", landmark['normalized_z'])
                
                # 发送置信度
                self.client.send_message(f"/pose/joint_{joint_id}/confidence", landmark['visibility'])
            
            # 发送元数据
            self.client.send_message("/pose/meta/timestamp", pose_data['timestamp'])
            self.client.send_message("/pose/meta/frame_count", pose_data['frame_count'])
            self.client.send_message("/pose/meta/fps", pose_data['fps'])
            
            # 发送特殊数据用于TouchDesigner可视化
            self._send_visualization_data(pose_data)
            
        except Exception as e:
            print(f"发送OSC数据失败: {e}")
    
    def _send_visualization_data(self, pose_data: Dict[str, Any]):
        """发送可视化数据"""
        landmarks = pose_data['landmarks']
        
        # 计算质心
        visible_landmarks = [lm for lm in landmarks if lm['visibility'] > 0.5]
        if visible_landmarks:
            center_x = np.mean([lm['x'] for lm in visible_landmarks])
            center_y = np.mean([lm['y'] for lm in visible_landmarks])
            center_z = np.mean([lm['z'] for lm in visible_landmarks])
            
            self.client.send_message("/pose/center/x", center_x)
            self.client.send_message("/pose/center/y", center_y)
            self.client.send_message("/pose/center/z", center_z)
        
        # 计算边界框
        if visible_landmarks:
            x_coords = [lm['x'] for lm in visible_landmarks]
            y_coords = [lm['y'] for lm in visible_landmarks]
            
            bbox_width = max(x_coords) - min(x_coords)
            bbox_height = max(y_coords) - min(y_coords)
            
            self.client.send_message("/pose/bbox/width", bbox_width)
            self.client.send_message("/pose/bbox/height", bbox_height)
        
        # 发送关键关节的特殊数据
        key_joints = {
            'head': 0,      # 鼻子
            'left_hand': 15,   # 左手腕
            'right_hand': 16,  # 右手腕
            'left_foot': 27,   # 左脚踝
            'right_foot': 28   # 右脚踝
        }
        
        for joint_name, joint_id in key_joints.items():
            if joint_id < len(landmarks):
                landmark = landmarks[joint_id]
                self.client.send_message(f"/pose/key/{joint_name}/x", landmark['x'])
                self.client.send_message(f"/pose/key/{joint_name}/y", landmark['y'])
                self.client.send_message(f"/pose/key/{joint_name}/z", landmark['z'])
    
    def send_control_signals(self, frame_count: int):
        """发送控制信号"""
        if not self.client:
            return
        
        try:
            # 发送节拍信号
            beat = int(frame_count / 30) % 4  # 每秒一拍，4拍循环
            self.client.send_message("/control/beat", beat)
            
            # 发送周期性信号
            cycle = np.sin(frame_count * 0.1)
            self.client.send_message("/control/cycle", cycle)
            
            # 发送随机触发信号
            if np.random.random() < 0.1:  # 10%概率
                self.client.send_message("/control/trigger", 1.0)
            else:
                self.client.send_message("/control/trigger", 0.0)
            
        except Exception as e:
            print(f"发送控制信号失败: {e}")
    
    async def run_demo(self, duration: int = 60):
        """运行TouchDesigner集成演示"""
        print(f"🎬 TouchDesigner集成演示开始 (时长: {duration}秒)")
        print(f"📡 发送数据到: {self.host}:{self.port}")
        print("💡 请确保TouchDesigner已启动并监听OSC端口")
        print()
        
        if not DEPENDENCIES_AVAILABLE:
            print("❌ 缺少依赖，无法运行演示")
            return
        
        frame_count = 0
        start_time = time.time()
        
        try:
            while time.time() - start_time < duration:
                # 创建模拟姿态数据
                pose_data = self.create_mock_pose_data(frame_count)
                
                # 发送到TouchDesigner
                self.send_pose_to_touchdesigner(pose_data)
                
                # 发送控制信号
                self.send_control_signals(frame_count)
                
                # 显示进度
                if frame_count % 30 == 0:  # 每秒显示一次
                    elapsed = time.time() - start_time
                    progress = elapsed / duration * 100
                    print(f"[{progress:5.1f}%] 帧: {frame_count}, 已发送 {len(pose_data['landmarks']) * 7} 条OSC消息")
                
                frame_count += 1
                await asyncio.sleep(1/30)  # 30 FPS
                
        except KeyboardInterrupt:
            print("\n演示被用户中断")
        
        print(f"\n✅ 演示完成! 总共发送了 {frame_count} 帧数据")


def create_touchdesigner_setup_guide():
    """创建TouchDesigner设置指南"""
    guide = """
# TouchDesigner设置指南

## 1. 创建OSC接收器

1. 在TouchDesigner中添加一个 `OSC In CHOP`
2. 设置参数:
   - Network Address: 127.0.0.1
   - Network Port: 9001
   - Active: On

## 2. 接收姿态数据

姿态数据将以以下格式发送:

### 关键点数据
- `/pose/joint_0/x` - 鼻子X坐标
- `/pose/joint_0/y` - 鼻子Y坐标  
- `/pose/joint_0/z` - 鼻子Z坐标
- `/pose/joint_0/confidence` - 置信度
- ... (重复到joint_32)

### 特殊数据
- `/pose/center/x,y,z` - 身体质心
- `/pose/bbox/width,height` - 边界框尺寸
- `/pose/key/head/x,y,z` - 头部位置
- `/pose/key/left_hand/x,y,z` - 左手位置
- `/pose/key/right_hand/x,y,z` - 右手位置

### 控制信号
- `/control/beat` - 节拍信号 (0-3)
- `/control/cycle` - 周期信号 (-1 to 1)
- `/control/trigger` - 随机触发 (0 or 1)

## 3. 数据可视化示例

### 基础粒子系统
1. 添加 `Add SOP` 创建33个点
2. 使用 `Transform SOP` 应用关键点位置
3. 添加 `Point SOP` 创建粒子
4. 使用 `Render TOP` 渲染

### 骨骼连接
1. 使用 `Line SOP` 连接相关关键点
2. 定义骨骼连接关系
3. 应用材质和渲染

### 交互效果
1. 使用手部位置控制参数
2. 根据动作触发效果
3. 实时响应姿态变化

## 4. 性能优化

- 使用 `Select CHOP` 过滤不需要的通道
- 调整 `OSC In CHOP` 的 Cook Type
- 使用 `Lag CHOP` 平滑数据
- 限制渲染帧率匹配输入数据

## 5. 故障排除

- 检查网络端口是否被占用
- 确认防火墙设置
- 验证OSC消息格式
- 使用TouchDesigner的Textport查看接收到的消息
"""
    
    with open("TouchDesigner_Setup_Guide.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("📖 TouchDesigner设置指南已创建: TouchDesigner_Setup_Guide.md")


async def main():
    """主函数"""
    print("🎭 TouchDesigner集成示例")
    print("=" * 50)
    
    # 创建设置指南
    create_touchdesigner_setup_guide()
    
    print("\n选择操作:")
    print("1. 运行TouchDesigner集成演示")
    print("2. 测试OSC连接")
    print("3. 发送测试数据")
    print("0. 退出")
    
    try:
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "1":
            duration = int(input("演示时长 (秒, 默认60): ") or "60")
            integration = TouchDesignerIntegration()
            await integration.run_demo(duration)
            
        elif choice == "2":
            integration = TouchDesignerIntegration()
            if integration.client:
                print("✅ OSC连接测试成功")
                integration.client.send_message("/test", ["connection_test", time.time()])
                print("已发送测试消息到 /test")
            else:
                print("❌ OSC连接失败")
                
        elif choice == "3":
            integration = TouchDesignerIntegration()
            if integration.client:
                print("发送测试数据...")
                for i in range(10):
                    integration.client.send_message(f"/test/value_{i}", i * 0.1)
                print("✅ 测试数据发送完成")
            else:
                print("❌ 无法发送测试数据")
                
        elif choice == "0":
            print("👋 再见!")
            
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"❌ 发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
