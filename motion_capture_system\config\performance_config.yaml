# 高性能低延迟配置文件
# 专门针对<100ms延迟目标优化

# 摄像头设置 - 优化延迟
camera:
  device_id: 0
  width: 320          # 降低分辨率以提高性能
  height: 240
  fps: 60             # 提高帧率以减少延迟

# MediaPipe设置 - 最低延迟配置
mediapipe:
  model_complexity: 0  # 使用最简单的模型
  min_detection_confidence: 0.3  # 降低置信度阈值
  min_tracking_confidence: 0.3
  enable_segmentation: false
  smooth_landmarks: false  # 禁用内置平滑以减少延迟

# 数据处理设置 - 最小化处理
data_processing:
  # 平滑滤波参数 - 轻量级配置
  smoothing:
    enabled: true
    method: "exponential"  # 最快的滤波方法
    alpha: 0.7  # 更高的响应性
  
  # 数据标准化 - 简化
  normalization:
    enabled: false  # 禁用标准化以节省时间
  
  # 关键点选择 - 只选择关键的关键点
  selected_keypoints: [0, 11, 12, 13, 14, 15, 16, 23, 24, 25, 26, 27, 28]  # 核心关键点

# 动作生成模型设置 - 最快配置
mdm:
  enabled: true
  model_type: "simple"  # 使用最简单的模型
  
  # 简单模型参数
  sequence_length: 3    # 减少序列长度
  prediction_length: 1  # 只预测下一帧
  
  # 禁用复杂模型
  lstm:
    enabled: false
  transformer:
    enabled: false

# OSC通信设置 - 高频率发送
osc:
  enabled: true
  host: "127.0.0.1"
  port: 9001
  
  # 消息格式设置 - 简化
  message_format:
    base_address: "/pose"
    include_confidence: false  # 减少数据量
    include_velocity: false
    include_acceleration: false
  
  # 发送设置 - 高频率
  send_rate: 60      # 匹配摄像头帧率
  batch_size: 13     # 只发送选中的关键点
  
  # 重试设置 - 快速失败
  retry:
    max_attempts: 1
    timeout: 0.01

# 性能设置 - 极致优化
performance:
  target_latency_ms: 50   # 更严格的延迟目标
  max_queue_size: 3       # 最小队列大小
  enable_frame_skip: true # 启用跳帧
  
  # 多线程设置 - 最大并发
  threading:
    capture_thread: true
    processing_thread: true
    osc_thread: true
    max_workers: 2  # 减少线程数以避免上下文切换开销

# 日志设置 - 最小化日志
logging:
  level: "WARNING"  # 只记录警告和错误
  file: "logs/performance.log"
  max_size: "5MB"
  backup_count: 2
  
  # 性能监控 - 轻量级
  performance_monitoring:
    enabled: true
    log_interval: 10  # 减少监控频率
    metrics: ["fps", "latency"]  # 只监控关键指标

# 调试设置 - 生产模式
debug:
  show_video: false     # 禁用视频显示
  show_landmarks: false # 禁用关键点显示
  save_data: false      # 禁用数据保存
