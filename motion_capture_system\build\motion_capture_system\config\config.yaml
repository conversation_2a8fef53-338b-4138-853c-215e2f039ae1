# 实时动作捕捉和生成系统配置文件

# 摄像头设置
camera:
  device_id: 0  # 摄像头设备ID
  width: 640    # 分辨率宽度
  height: 480   # 分辨率高度
  fps: 30       # 帧率

# MediaPipe姿态检测设置
mediapipe:
  model_complexity: 1  # 模型复杂度 (0, 1, 2)
  min_detection_confidence: 0.5
  min_tracking_confidence: 0.5
  enable_segmentation: false
  smooth_landmarks: true

# 数据处理设置
data_processing:
  # 平滑滤波参数
  smoothing:
    enabled: true
    method: "kalman"  # "kalman", "moving_average", "exponential"
    window_size: 5    # 移动平均窗口大小
    alpha: 0.3        # 指数平滑参数
  
  # 数据标准化
  normalization:
    enabled: true
    method: "minmax"  # "minmax", "zscore"
  
  # 关键点选择（33个关键点的索引）
  selected_keypoints: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]

# 动作生成模型设置
mdm:
  enabled: true
  model_type: "lstm"  # "lstm", "transformer", "simple"
  
  # LSTM模型参数
  lstm:
    hidden_size: 128
    num_layers: 2
    dropout: 0.1
    sequence_length: 10  # 输入序列长度
    prediction_length: 5  # 预测序列长度
  
  # Transformer模型参数
  transformer:
    d_model: 256
    nhead: 8
    num_layers: 4
    dim_feedforward: 512
    dropout: 0.1
    sequence_length: 10
    prediction_length: 5
  
  # 模型文件路径
  model_path: "models/motion_model.pth"
  
  # 训练参数
  training:
    enabled: false
    batch_size: 32
    learning_rate: 0.001
    epochs: 100

# OSC通信设置
osc:
  enabled: true
  host: "127.0.0.1"  # TouchDesigner主机地址
  port: 9001         # OSC端口
  
  # 消息格式设置
  message_format:
    base_address: "/pose"
    include_confidence: true
    include_velocity: true
    include_acceleration: false
  
  # 发送设置
  send_rate: 30      # 发送频率 (Hz)
  batch_size: 33     # 批量发送关键点数量
  
  # 重试设置
  retry:
    max_attempts: 3
    timeout: 0.1

# 性能设置
performance:
  target_latency_ms: 100  # 目标延迟（毫秒）
  max_queue_size: 10      # 最大队列大小
  enable_frame_skip: true # 启用跳帧
  
  # 多线程设置
  threading:
    capture_thread: true
    processing_thread: true
    osc_thread: true
    max_workers: 4

# 日志设置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/motion_capture.log"
  max_size: "10MB"
  backup_count: 5
  
  # 性能监控
  performance_monitoring:
    enabled: true
    log_interval: 5  # 秒
    metrics: ["fps", "latency", "cpu_usage", "memory_usage"]

# 调试设置
debug:
  show_video: true      # 显示视频窗口
  show_landmarks: true  # 显示关键点
  save_data: false      # 保存原始数据
  data_path: "data/"    # 数据保存路径
