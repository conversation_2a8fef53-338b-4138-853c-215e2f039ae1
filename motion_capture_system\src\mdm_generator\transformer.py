"""
Transformer动作生成模型
提供基于Transformer架构的动作预测和生成功能
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from loguru import logger


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        """
        初始化位置编码
        
        Args:
            d_model: 模型维度
            max_len: 最大序列长度
        """
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [seq_len, batch_size, d_model]
            
        Returns:
            添加位置编码后的张量
        """
        return x + self.pe[:x.size(0), :]


class TransformerMotionModel(nn.Module):
    """Transformer动作预测模型"""
    
    def __init__(self,
                 input_size: int = 99,  # 33个关键点 * 3个坐标
                 d_model: int = 256,
                 nhead: int = 8,
                 num_layers: int = 4,
                 dim_feedforward: int = 512,
                 dropout: float = 0.1,
                 sequence_length: int = 10,
                 prediction_length: int = 5):
        """
        初始化Transformer模型
        
        Args:
            input_size: 输入特征维度
            d_model: 模型维度
            nhead: 注意力头数
            num_layers: Transformer层数
            dim_feedforward: 前馈网络维度
            dropout: Dropout率
            sequence_length: 输入序列长度
            prediction_length: 预测序列长度
        """
        super(TransformerMotionModel, self).__init__()
        
        self.input_size = input_size
        self.d_model = d_model
        self.sequence_length = sequence_length
        self.prediction_length = prediction_length
        
        # 输入投影层
        self.input_projection = nn.Linear(input_size, d_model)
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=False  # [seq_len, batch_size, d_model]
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # 输出投影层
        self.output_projection = nn.Linear(d_model, input_size)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        logger.info(f"Transformer模型初始化完成: d_model={d_model}, nhead={nhead}, num_layers={num_layers}")
    
    def forward(self, src: torch.Tensor, src_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            src: 输入张量 [batch_size, seq_len, input_size]
            src_mask: 源序列掩码
            
        Returns:
            输出张量 [batch_size, seq_len, input_size]
        """
        # 转换维度: [batch_size, seq_len, input_size] -> [seq_len, batch_size, input_size]
        src = src.transpose(0, 1)
        
        # 输入投影
        src = self.input_projection(src) * math.sqrt(self.d_model)
        
        # 添加位置编码
        src = self.pos_encoder(src)
        
        # 应用dropout
        src = self.dropout(src)
        
        # Transformer编码
        output = self.transformer_encoder(src, src_mask)
        
        # 输出投影
        output = self.output_projection(output)
        
        # 转换维度回来: [seq_len, batch_size, input_size] -> [batch_size, seq_len, input_size]
        output = output.transpose(0, 1)
        
        return output
    
    def predict_sequence(self, input_sequence: torch.Tensor) -> torch.Tensor:
        """
        预测未来序列
        
        Args:
            input_sequence: 输入序列 [batch_size, sequence_length, input_size]
            
        Returns:
            预测序列 [batch_size, prediction_length, input_size]
        """
        self.eval()
        with torch.no_grad():
            batch_size = input_sequence.size(0)
            device = input_sequence.device
            
            # 使用输入序列进行编码
            encoded = self.forward(input_sequence)
            
            # 使用最后几个时间步的输出作为预测的起始点
            predictions = []
            current_sequence = input_sequence.clone()
            
            for _ in range(self.prediction_length):
                # 预测下一个时间步
                output = self.forward(current_sequence)
                next_frame = output[:, -1:, :]  # 取最后一个时间步
                
                predictions.append(next_frame)
                
                # 更新序列：移除第一个时间步，添加预测的时间步
                current_sequence = torch.cat([current_sequence[:, 1:, :], next_frame], dim=1)
            
            # 拼接预测结果
            predicted_sequence = torch.cat(predictions, dim=1)
            
        return predicted_sequence
    
    def generate_square_subsequent_mask(self, sz: int) -> torch.Tensor:
        """
        生成方形的后续掩码（用于自回归生成）
        
        Args:
            sz: 序列长度
            
        Returns:
            掩码张量
        """
        mask = (torch.triu(torch.ones(sz, sz)) == 1).transpose(0, 1)
        mask = mask.float().masked_fill(mask == 0, float('-inf')).masked_fill(mask == 1, float(0.0))
        return mask


class LightweightTransformer(nn.Module):
    """轻量级Transformer模型（针对实时性能优化）"""
    
    def __init__(self,
                 input_size: int = 99,
                 d_model: int = 128,  # 减小模型维度
                 nhead: int = 4,      # 减少注意力头数
                 num_layers: int = 2, # 减少层数
                 dropout: float = 0.1,
                 sequence_length: int = 10,
                 prediction_length: int = 5):
        """
        初始化轻量级Transformer模型
        
        Args:
            input_size: 输入特征维度
            d_model: 模型维度
            nhead: 注意力头数
            num_layers: Transformer层数
            dropout: Dropout率
            sequence_length: 输入序列长度
            prediction_length: 预测序列长度
        """
        super(LightweightTransformer, self).__init__()
        
        self.input_size = input_size
        self.d_model = d_model
        self.sequence_length = sequence_length
        self.prediction_length = prediction_length
        
        # 简化的输入投影
        self.input_projection = nn.Linear(input_size, d_model)
        
        # 简化的位置编码（学习式）
        self.pos_embedding = nn.Parameter(torch.randn(sequence_length + prediction_length, d_model))
        
        # 轻量级注意力层
        self.attention_layers = nn.ModuleList([
            nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=True)
            for _ in range(num_layers)
        ])
        
        # 前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model)
        )
        
        # 输出投影
        self.output_projection = nn.Linear(d_model, input_size)
        
        # 层归一化
        self.layer_norms = nn.ModuleList([nn.LayerNorm(d_model) for _ in range(num_layers * 2)])
        
        self.dropout = nn.Dropout(dropout)
        
        logger.info(f"轻量级Transformer模型初始化完成: d_model={d_model}, nhead={nhead}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, input_size]
            
        Returns:
            输出张量 [batch_size, seq_len, input_size]
        """
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)
        
        # 添加位置编码
        x = x + self.pos_embedding[:seq_len].unsqueeze(0)
        
        # 应用dropout
        x = self.dropout(x)
        
        # 多层注意力
        for i, attention in enumerate(self.attention_layers):
            # 自注意力
            residual = x
            x = self.layer_norms[i * 2](x)
            attn_output, _ = attention(x, x, x)
            x = residual + self.dropout(attn_output)
            
            # 前馈网络
            residual = x
            x = self.layer_norms[i * 2 + 1](x)
            ff_output = self.feed_forward(x)
            x = residual + self.dropout(ff_output)
        
        # 输出投影
        output = self.output_projection(x)
        
        return output
    
    def predict_sequence(self, input_sequence: torch.Tensor) -> torch.Tensor:
        """
        预测未来序列（优化版本）
        
        Args:
            input_sequence: 输入序列 [batch_size, sequence_length, input_size]
            
        Returns:
            预测序列 [batch_size, prediction_length, input_size]
        """
        self.eval()
        with torch.no_grad():
            # 直接使用前向传播进行预测
            extended_input = torch.zeros(
                input_sequence.size(0), 
                input_sequence.size(1) + self.prediction_length, 
                input_sequence.size(2),
                device=input_sequence.device
            )
            extended_input[:, :input_sequence.size(1), :] = input_sequence
            
            # 前向传播
            output = self.forward(extended_input)
            
            # 返回预测部分
            predictions = output[:, input_sequence.size(1):, :]
            
        return predictions


class MotionTransformer:
    """动作Transformer包装器"""
    
    def __init__(self, config: Dict):
        """
        初始化动作Transformer
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 选择模型类型
        model_config = config.get('transformer', {})
        if model_config.get('lightweight', True):
            self.model = LightweightTransformer(
                input_size=model_config.get('input_size', 99),
                d_model=model_config.get('d_model', 128),
                nhead=model_config.get('nhead', 4),
                num_layers=model_config.get('num_layers', 2),
                dropout=model_config.get('dropout', 0.1),
                sequence_length=model_config.get('sequence_length', 10),
                prediction_length=model_config.get('prediction_length', 5)
            )
        else:
            self.model = TransformerMotionModel(
                input_size=model_config.get('input_size', 99),
                d_model=model_config.get('d_model', 256),
                nhead=model_config.get('nhead', 8),
                num_layers=model_config.get('num_layers', 4),
                dim_feedforward=model_config.get('dim_feedforward', 512),
                dropout=model_config.get('dropout', 0.1),
                sequence_length=model_config.get('sequence_length', 10),
                prediction_length=model_config.get('prediction_length', 5)
            )
        
        self.model.to(self.device)
        
        # 加载预训练模型（如果存在）
        model_path = config.get('model_path')
        if model_path:
            self.load_model(model_path)
        
        logger.info("动作Transformer初始化完成")
    
    def predict(self, input_sequence: np.ndarray) -> np.ndarray:
        """
        预测动作序列
        
        Args:
            input_sequence: 输入序列 [sequence_length, feature_dim]
            
        Returns:
            预测序列 [prediction_length, feature_dim]
        """
        # 转换为张量
        input_tensor = torch.FloatTensor(input_sequence).unsqueeze(0).to(self.device)
        
        # 预测
        with torch.no_grad():
            predictions = self.model.predict_sequence(input_tensor)
        
        # 转换回numpy
        return predictions.cpu().numpy().squeeze(0)
    
    def load_model(self, model_path: str):
        """
        加载模型
        
        Args:
            model_path: 模型文件路径
        """
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            logger.info(f"模型加载成功: {model_path}")
        except Exception as e:
            logger.warning(f"模型加载失败: {e}")
    
    def save_model(self, model_path: str):
        """
        保存模型
        
        Args:
            model_path: 模型保存路径
        """
        try:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'config': self.config
            }, model_path)
            logger.info(f"模型保存成功: {model_path}")
        except Exception as e:
            logger.error(f"模型保存失败: {e}")
