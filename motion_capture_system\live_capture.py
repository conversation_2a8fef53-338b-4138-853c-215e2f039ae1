#!/usr/bin/env python3
"""
实时动作捕捉可视化程序
显示摄像头画面并实时绘制人体姿态关键点
"""

import cv2
import numpy as np
import time
import sys
from typing import Optional, Tuple, List
import argparse

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    print("警告: MediaPipe未安装，将使用模拟模式")

class LiveMotionCapture:
    """实时动作捕捉可视化器"""
    
    def __init__(self, camera_id: int = 0, width: int = 640, height: int = 480):
        """
        初始化实时捕捉器
        
        Args:
            camera_id: 摄像头设备ID
            width: 视频宽度
            height: 视频高度
        """
        self.camera_id = camera_id
        self.width = width
        self.height = height
        self.cap = None
        self.pose = None
        self.mp_pose = None
        self.mp_drawing = None
        self.mp_drawing_styles = None
        
        # 性能统计
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        self.frame_times = []
        
        # 显示选项
        self.show_landmarks = True
        self.show_connections = True
        self.show_confidence = True
        self.show_fps = True
        self.recording = False
        self.video_writer = None
        
        # 颜色配置
        self.colors = {
            'landmarks': (0, 255, 0),      # 绿色关键点
            'connections': (255, 0, 0),    # 红色连接线
            'text': (255, 255, 255),       # 白色文字
            'background': (0, 0, 0),       # 黑色背景
            'high_confidence': (0, 255, 0), # 高置信度 - 绿色
            'low_confidence': (0, 0, 255)   # 低置信度 - 红色
        }
        
    def initialize(self) -> bool:
        """初始化摄像头和MediaPipe"""
        print("🎥 初始化摄像头...")
        
        # 初始化摄像头
        self.cap = cv2.VideoCapture(self.camera_id)
        if not self.cap.isOpened():
            print(f"❌ 无法打开摄像头 {self.camera_id}")
            return False
        
        # 设置摄像头参数
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
        self.cap.set(cv2.CAP_PROP_FPS, 30)
        
        print(f"✅ 摄像头初始化成功 - 分辨率: {self.width}x{self.height}")
        
        # 初始化MediaPipe
        if MEDIAPIPE_AVAILABLE:
            print("🤖 初始化MediaPipe...")
            self.mp_pose = mp.solutions.pose
            self.mp_drawing = mp.solutions.drawing_utils
            self.mp_drawing_styles = mp.solutions.drawing_styles
            
            self.pose = self.mp_pose.Pose(
                static_image_mode=False,
                model_complexity=1,
                smooth_landmarks=True,
                enable_segmentation=False,
                smooth_segmentation=True,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            print("✅ MediaPipe初始化成功")
        else:
            print("⚠️  MediaPipe不可用，将显示原始视频")
        
        return True
    
    def process_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, Optional[any]]:
        """
        处理单帧图像
        
        Args:
            frame: 输入图像帧
            
        Returns:
            处理后的图像和姿态结果
        """
        if not MEDIAPIPE_AVAILABLE or self.pose is None:
            return frame, None
        
        # 转换颜色空间
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # MediaPipe姿态检测
        results = self.pose.process(rgb_frame)
        
        return frame, results
    
    def draw_landmarks(self, frame: np.ndarray, results: any) -> np.ndarray:
        """
        在图像上绘制姿态关键点
        
        Args:
            frame: 输入图像
            results: MediaPipe检测结果
            
        Returns:
            绘制后的图像
        """
        if not results or not results.pose_landmarks:
            return frame
        
        h, w, _ = frame.shape
        
        # 绘制连接线
        if self.show_connections:
            # 使用简化的绘制方式
            connection_drawing_spec = self.mp_drawing.DrawingSpec(
                color=self.colors['connections'],
                thickness=2,
                circle_radius=1
            )
            self.mp_drawing.draw_landmarks(
                frame,
                results.pose_landmarks,
                self.mp_pose.POSE_CONNECTIONS,
                landmark_drawing_spec=None,
                connection_drawing_spec=connection_drawing_spec
            )
        
        # 绘制关键点
        if self.show_landmarks:
            for idx, landmark in enumerate(results.pose_landmarks.landmark):
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                confidence = landmark.visibility
                
                # 根据置信度选择颜色
                if confidence > 0.7:
                    color = self.colors['high_confidence']
                    radius = 6
                elif confidence > 0.5:
                    color = (0, 255, 255)  # 黄色
                    radius = 4
                else:
                    color = self.colors['low_confidence']
                    radius = 3
                
                # 绘制关键点
                cv2.circle(frame, (x, y), radius, color, -1)
                cv2.circle(frame, (x, y), radius + 1, (0, 0, 0), 1)
                
                # 显示置信度
                if self.show_confidence and confidence > 0.5:
                    cv2.putText(frame, f"{confidence:.2f}", (x + 10, y - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.3, self.colors['text'], 1)
        
        return frame
    
    def draw_info_panel(self, frame: np.ndarray) -> np.ndarray:
        """
        绘制信息面板
        
        Args:
            frame: 输入图像
            
        Returns:
            绘制后的图像
        """
        h, w, _ = frame.shape
        
        # 创建半透明背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (300, 120), self.colors['background'], -1)
        frame = cv2.addWeighted(frame, 0.7, overlay, 0.3, 0)
        
        # 显示信息
        y_offset = 30
        line_height = 20
        
        # FPS信息
        if self.show_fps:
            cv2.putText(frame, f"FPS: {self.current_fps:.1f}", (20, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, self.colors['text'], 2)
            y_offset += line_height
        
        # 分辨率信息
        cv2.putText(frame, f"分辨率: {w}x{h}", (20, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, self.colors['text'], 1)
        y_offset += line_height
        
        # MediaPipe状态
        status = "启用" if MEDIAPIPE_AVAILABLE else "禁用"
        cv2.putText(frame, f"MediaPipe: {status}", (20, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, self.colors['text'], 1)
        y_offset += line_height
        
        # 录制状态
        if self.recording:
            cv2.putText(frame, "🔴 录制中", (20, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
        
        # 控制提示
        controls = [
            "按键控制:",
            "Q - 退出",
            "L - 切换关键点",
            "C - 切换连接线",
            "F - 切换FPS显示",
            "R - 开始/停止录制",
            "S - 截图"
        ]
        
        start_y = h - len(controls) * 20 - 10
        for i, control in enumerate(controls):
            cv2.putText(frame, control, (10, start_y + i * 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, self.colors['text'], 1)
        
        return frame
    
    def update_fps(self):
        """更新FPS计算"""
        current_time = time.time()
        self.frame_times.append(current_time)
        
        # 保持最近1秒的帧时间
        self.frame_times = [t for t in self.frame_times if current_time - t <= 1.0]
        
        # 计算FPS
        if len(self.frame_times) > 1:
            self.current_fps = len(self.frame_times) - 1
    
    def handle_key_input(self, key: int) -> bool:
        """
        处理键盘输入
        
        Args:
            key: 按键码
            
        Returns:
            是否继续运行
        """
        if key == ord('q') or key == ord('Q') or key == 27:  # Q或ESC退出
            return False
        elif key == ord('l') or key == ord('L'):  # 切换关键点显示
            self.show_landmarks = not self.show_landmarks
            print(f"关键点显示: {'开启' if self.show_landmarks else '关闭'}")
        elif key == ord('c') or key == ord('C'):  # 切换连接线显示
            self.show_connections = not self.show_connections
            print(f"连接线显示: {'开启' if self.show_connections else '关闭'}")
        elif key == ord('f') or key == ord('F'):  # 切换FPS显示
            self.show_fps = not self.show_fps
            print(f"FPS显示: {'开启' if self.show_fps else '关闭'}")
        elif key == ord('r') or key == ord('R'):  # 录制切换
            self.toggle_recording()
        elif key == ord('s') or key == ord('S'):  # 截图
            self.take_screenshot()
        
        return True
    
    def toggle_recording(self):
        """切换录制状态"""
        if not self.recording:
            # 开始录制
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"motion_capture_{timestamp}.mp4"
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            self.video_writer = cv2.VideoWriter(filename, fourcc, 30.0, (self.width, self.height))
            self.recording = True
            print(f"🔴 开始录制: {filename}")
        else:
            # 停止录制
            if self.video_writer:
                self.video_writer.release()
                self.video_writer = None
            self.recording = False
            print("⏹️  录制已停止")
    
    def take_screenshot(self):
        """截图"""
        if hasattr(self, 'current_frame'):
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.jpg"
            cv2.imwrite(filename, self.current_frame)
            print(f"📸 截图已保存: {filename}")
    
    def run(self):
        """运行实时捕捉"""
        if not self.initialize():
            return
        
        print("\n🎬 实时动作捕捉已启动!")
        print("=" * 50)
        print("控制说明:")
        print("- Q: 退出程序")
        print("- L: 切换关键点显示")
        print("- C: 切换连接线显示")
        print("- F: 切换FPS显示")
        print("- R: 开始/停止录制")
        print("- S: 截图")
        print("=" * 50)
        
        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    print("❌ 无法读取摄像头画面")
                    break
                
                # 水平翻转图像（镜像效果）
                frame = cv2.flip(frame, 1)
                
                # 处理帧
                processed_frame, results = self.process_frame(frame)
                
                # 绘制姿态关键点
                if results:
                    processed_frame = self.draw_landmarks(processed_frame, results)
                
                # 绘制信息面板
                processed_frame = self.draw_info_panel(processed_frame)
                
                # 更新FPS
                self.update_fps()
                
                # 录制视频
                if self.recording and self.video_writer:
                    self.video_writer.write(processed_frame)
                
                # 保存当前帧用于截图
                self.current_frame = processed_frame.copy()
                
                # 显示图像
                cv2.imshow('实时动作捕捉 - Motion Capture Live', processed_frame)
                
                # 处理键盘输入
                key = cv2.waitKey(1) & 0xFF
                if not self.handle_key_input(key):
                    break
                    
        except KeyboardInterrupt:
            print("\n⏹️  程序被用户中断")
        except Exception as e:
            print(f"❌ 运行时错误: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 清理资源...")
        
        if self.cap:
            self.cap.release()
        
        if self.video_writer:
            self.video_writer.release()
        
        cv2.destroyAllWindows()
        
        if self.pose:
            self.pose.close()
        
        print("✅ 资源清理完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='实时动作捕捉可视化程序')
    parser.add_argument('--camera', type=int, default=0, help='摄像头设备ID (默认: 0)')
    parser.add_argument('--width', type=int, default=640, help='视频宽度 (默认: 640)')
    parser.add_argument('--height', type=int, default=480, help='视频高度 (默认: 480)')
    
    args = parser.parse_args()
    
    print("🎭 实时动作捕捉可视化程序")
    print("=" * 40)
    print(f"摄像头ID: {args.camera}")
    print(f"分辨率: {args.width}x{args.height}")
    print("=" * 40)
    
    # 创建并运行捕捉器
    capture = LiveMotionCapture(args.camera, args.width, args.height)
    capture.run()


if __name__ == "__main__":
    main()
