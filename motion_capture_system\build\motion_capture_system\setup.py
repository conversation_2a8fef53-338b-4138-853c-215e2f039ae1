#!/usr/bin/env python3
"""
实时动作捕捉和生成系统 - 安装配置
用于pip安装和PyPI分发
"""

from setuptools import setup, find_packages
from pathlib import Path
import re

# 读取README文件
def read_readme():
    """读取README文件内容"""
    readme_path = Path(__file__).parent / "README.md"
    if readme_path.exists():
        with open(readme_path, "r", encoding="utf-8") as f:
            return f.read()
    return "实时动作捕捉和生成系统"

# 读取requirements文件
def read_requirements():
    """读取requirements文件"""
    req_path = Path(__file__).parent / "requirements.txt"
    requirements = []
    
    if req_path.exists():
        with open(req_path, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#"):
                    # 移除注释
                    if "#" in line:
                        line = line.split("#")[0].strip()
                    requirements.append(line)
    
    return requirements

# 获取版本号
def get_version():
    """从__init__.py获取版本号"""
    init_path = Path(__file__).parent / "src" / "__init__.py"
    
    if init_path.exists():
        with open(init_path, "r", encoding="utf-8") as f:
            content = f.read()
            version_match = re.search(r"__version__\s*=\s*['\"]([^'\"]*)['\"]", content)
            if version_match:
                return version_match.group(1)
    
    return "1.0.0"

# 项目元数据
PROJECT_NAME = "motion-capture-system"
VERSION = get_version()
DESCRIPTION = "实时动作捕捉和生成系统"
LONG_DESCRIPTION = read_readme()
AUTHOR = "Motion Capture Team"
AUTHOR_EMAIL = "<EMAIL>"
URL = "https://github.com/your-username/motion-capture-system"
LICENSE = "MIT"

# Python版本要求
PYTHON_REQUIRES = ">=3.8"

# 依赖包
INSTALL_REQUIRES = read_requirements()

# 可选依赖
EXTRAS_REQUIRE = {
    "dev": [
        "pytest>=6.0",
        "pytest-cov>=2.0",
        "pytest-asyncio>=0.18",
        "black>=22.0",
        "flake8>=4.0",
        "mypy>=0.900",
    ],
    "gpu": [
        "torch>=1.12.0",
        "torchvision>=0.13.0",
    ],
    "visualization": [
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        "plotly>=5.0.0",
    ],
    "web": [
        "fastapi>=0.75.0",
        "uvicorn>=0.17.0",
        "websockets>=10.0",
    ]
}

# 所有可选依赖
EXTRAS_REQUIRE["all"] = [
    dep for deps in EXTRAS_REQUIRE.values() for dep in deps
]

# 分类器
CLASSIFIERS = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Multimedia :: Video :: Capture",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

# 关键词
KEYWORDS = [
    "motion capture",
    "pose detection",
    "mediapipe",
    "osc",
    "touchdesigner",
    "real-time",
    "computer vision",
    "machine learning",
    "deep learning",
    "lstm",
    "transformer",
]

# 项目URL
PROJECT_URLS = {
    "Bug Reports": f"{URL}/issues",
    "Source": URL,
    "Documentation": f"{URL}/wiki",
    "Changelog": f"{URL}/blob/main/CHANGELOG.md",
}

# 控制台脚本
CONSOLE_SCRIPTS = [
    "motion-capture=src.main:main",
    "motion-capture-demo=demo:main",
    "motion-capture-optimizer=system_optimizer:main",
    "motion-capture-benchmark=benchmark_performance:main",
]

# 数据文件
PACKAGE_DATA = {
    "": [
        "config/*.yaml",
        "config/*.yml",
        "examples/*.py",
        "examples/*.md",
        "*.md",
        "*.txt",
        "*.json",
    ]
}

# 包含的数据文件
INCLUDE_PACKAGE_DATA = True

# 排除的包
EXCLUDE_PACKAGES = [
    "tests*",
    "build*",
    "dist*",
    "venv*",
    "motion_env*",
    "logs*",
    "data*",
    "models*",
    "temp*",
]

def setup_package():
    """设置包配置"""
    
    # 查找包
    packages = find_packages(
        where=".",
        exclude=EXCLUDE_PACKAGES
    )
    
    # 设置配置
    setup(
        name=PROJECT_NAME,
        version=VERSION,
        description=DESCRIPTION,
        long_description=LONG_DESCRIPTION,
        long_description_content_type="text/markdown",
        author=AUTHOR,
        author_email=AUTHOR_EMAIL,
        url=URL,
        project_urls=PROJECT_URLS,
        license=LICENSE,
        
        # 包信息
        packages=packages,
        package_dir={"": "."},
        package_data=PACKAGE_DATA,
        include_package_data=INCLUDE_PACKAGE_DATA,
        
        # 依赖
        python_requires=PYTHON_REQUIRES,
        install_requires=INSTALL_REQUIRES,
        extras_require=EXTRAS_REQUIRE,
        
        # 脚本
        entry_points={
            "console_scripts": CONSOLE_SCRIPTS,
        },
        
        # 元数据
        classifiers=CLASSIFIERS,
        keywords=", ".join(KEYWORDS),
        
        # 其他选项
        zip_safe=False,
        platforms=["any"],
    )

if __name__ == "__main__":
    setup_package()
