#!/usr/bin/env python3
"""
系统结构测试脚本
验证项目结构和基本导入功能（不需要外部依赖）
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_project_structure():
    """测试项目结构"""
    print("测试项目结构...")
    
    required_files = [
        "main.py",
        "run_system.py",
        "config/config.yaml",
        "src/__init__.py",
        "src/mediapipe_capture/__init__.py",
        "src/mediapipe_capture/pose_detector.py",
        "src/mediapipe_capture/data_processor.py",
        "src/mdm_generator/__init__.py",
        "src/mdm_generator/model.py",
        "src/mdm_generator/transformer.py",
        "src/mdm_generator/motion_predictor.py",
        "src/osc_communication/__init__.py",
        "src/osc_communication/osc_sender.py",
        "src/utils/__init__.py",
        "src/utils/filters.py",
        "src/utils/performance.py",
        "src/utils/error_handler.py",
        "tests/test_basic_functionality.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        print("✅ 所有必需文件都存在")
        return True

def test_basic_imports():
    """测试基本导入（不需要外部依赖）"""
    print("\n测试基本导入...")
    
    try:
        # 测试内置模块导入
        import asyncio
        import time
        import threading
        import json
        import yaml
        print("✅ 内置模块导入成功")
        
        # 测试项目模块结构（不实际导入，只检查语法）
        module_files = [
            "src/mediapipe_capture/pose_detector.py",
            "src/mediapipe_capture/data_processor.py",
            "src/mdm_generator/model.py",
            "src/mdm_generator/transformer.py",
            "src/mdm_generator/motion_predictor.py",
            "src/osc_communication/osc_sender.py",
            "src/utils/filters.py",
            "src/utils/performance.py",
            "src/utils/error_handler.py"
        ]
        
        syntax_errors = []
        for module_file in module_files:
            try:
                with open(module_file, 'r', encoding='utf-8') as f:
                    code = f.read()
                compile(code, module_file, 'exec')
            except SyntaxError as e:
                syntax_errors.append(f"{module_file}: {e}")
            except Exception as e:
                # 忽略导入错误，只关心语法错误
                pass
        
        if syntax_errors:
            print(f"❌ 语法错误: {syntax_errors}")
            return False
        else:
            print("✅ 所有模块语法检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def test_config_file():
    """测试配置文件"""
    print("\n测试配置文件...")
    
    try:
        import yaml
        with open('config/config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必需的配置项
        required_sections = ['camera', 'mediapipe', 'mdm', 'osc', 'performance', 'logging']
        missing_sections = []
        
        for section in required_sections:
            if section not in config:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ 配置文件缺少部分: {missing_sections}")
            return False
        else:
            print("✅ 配置文件结构正确")
            return True
            
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_class_definitions():
    """测试类定义（不实例化）"""
    print("\n测试类定义...")
    
    try:
        # 检查主要类是否定义正确
        class_checks = []
        
        # 检查PoseDetector类
        with open('src/mediapipe_capture/pose_detector.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'class PoseDetector:' in content:
                class_checks.append("PoseDetector")
        
        # 检查MotionPredictor类
        with open('src/mdm_generator/motion_predictor.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'class MotionPredictor:' in content:
                class_checks.append("MotionPredictor")
        
        # 检查OSCSender类
        with open('src/osc_communication/osc_sender.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'class OSCSender:' in content:
                class_checks.append("OSCSender")
        
        # 检查DataFilter类
        with open('src/utils/filters.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'class DataFilter:' in content:
                class_checks.append("DataFilter")
        
        # 检查PerformanceMonitor类
        with open('src/utils/performance.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'class PerformanceMonitor:' in content:
                class_checks.append("PerformanceMonitor")
        
        expected_classes = ["PoseDetector", "MotionPredictor", "OSCSender", "DataFilter", "PerformanceMonitor"]
        missing_classes = [cls for cls in expected_classes if cls not in class_checks]
        
        if missing_classes:
            print(f"❌ 缺少类定义: {missing_classes}")
            return False
        else:
            print("✅ 所有主要类定义正确")
            return True
            
    except Exception as e:
        print(f"❌ 类定义测试失败: {e}")
        return False

def test_method_signatures():
    """测试主要方法签名"""
    print("\n测试方法签名...")
    
    try:
        method_checks = []
        
        # 检查PoseDetector的主要方法
        with open('src/mediapipe_capture/pose_detector.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'def get_pose(' in content:
                method_checks.append("PoseDetector.get_pose")
            if 'def get_pose_async(' in content:
                method_checks.append("PoseDetector.get_pose_async")
        
        # 检查MotionPredictor的主要方法
        with open('src/mdm_generator/motion_predictor.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'def predict(' in content:
                method_checks.append("MotionPredictor.predict")
            if 'def predict_async(' in content:
                method_checks.append("MotionPredictor.predict_async")
        
        # 检查OSCSender的主要方法
        with open('src/osc_communication/osc_sender.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'def send_pose_data(' in content:
                method_checks.append("OSCSender.send_pose_data")
            if 'def send_pose_data_async(' in content:
                method_checks.append("OSCSender.send_pose_data_async")
        
        expected_methods = [
            "PoseDetector.get_pose",
            "PoseDetector.get_pose_async", 
            "MotionPredictor.predict",
            "MotionPredictor.predict_async",
            "OSCSender.send_pose_data",
            "OSCSender.send_pose_data_async"
        ]
        
        missing_methods = [method for method in expected_methods if method not in method_checks]
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有主要方法签名正确")
            return True
            
    except Exception as e:
        print(f"❌ 方法签名测试失败: {e}")
        return False

def test_documentation():
    """测试文档文件"""
    print("\n测试文档文件...")
    
    try:
        doc_files = [
            "README.md",
            "API_DOCUMENTATION.md"
        ]
        
        missing_docs = []
        for doc_file in doc_files:
            if not Path(doc_file).exists():
                missing_docs.append(doc_file)
        
        if missing_docs:
            print(f"❌ 缺少文档文件: {missing_docs}")
            return False
        else:
            print("✅ 所有文档文件存在")
            return True
            
    except Exception as e:
        print(f"❌ 文档测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("实时动作捕捉和生成系统 - 结构测试")
    print("=" * 60)
    
    tests = [
        ("项目结构", test_project_structure),
        ("基本导入", test_basic_imports),
        ("配置文件", test_config_file),
        ("类定义", test_class_definitions),
        ("方法签名", test_method_signatures),
        ("文档文件", test_documentation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有结构测试通过！系统代码结构正确。")
        print("\n下一步:")
        print("1. 安装依赖: python install.py")
        print("2. 运行功能测试: python test_modules.py")
        print("3. 启动系统: python run_system.py")
    else:
        print(f"⚠️  有 {total - passed} 个测试失败，请检查相关文件。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
