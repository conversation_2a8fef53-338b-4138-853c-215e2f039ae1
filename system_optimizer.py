#!/usr/bin/env python3
"""
系统优化器
根据硬件配置和性能要求自动优化系统参数
"""

import sys
import time
import platform
from pathlib import Path
from typing import Dict, Any, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    import yaml
    import psutil
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    print("警告: 某些依赖不可用，将使用默认配置")
    DEPENDENCIES_AVAILABLE = False


class SystemOptimizer:
    """系统优化器类"""
    
    def __init__(self):
        """初始化优化器"""
        self.hardware_info = self._detect_hardware()
        self.optimization_profiles = {
            'ultra_low_latency': {
                'target_latency_ms': 30,
                'camera_resolution': (160, 120),
                'model_complexity': 0,
                'enable_prediction': False,
                'enable_filtering': False
            },
            'low_latency': {
                'target_latency_ms': 50,
                'camera_resolution': (320, 240),
                'model_complexity': 0,
                'enable_prediction': True,
                'enable_filtering': True
            },
            'balanced': {
                'target_latency_ms': 100,
                'camera_resolution': (640, 480),
                'model_complexity': 1,
                'enable_prediction': True,
                'enable_filtering': True
            },
            'high_quality': {
                'target_latency_ms': 200,
                'camera_resolution': (1280, 720),
                'model_complexity': 2,
                'enable_prediction': True,
                'enable_filtering': True
            }
        }
    
    def _detect_hardware(self) -> Dict[str, Any]:
        """检测硬件配置"""
        hardware_info = {
            'platform': platform.system(),
            'cpu_count': 1,
            'memory_gb': 4,
            'has_gpu': False
        }
        
        if DEPENDENCIES_AVAILABLE:
            try:
                hardware_info.update({
                    'cpu_count': psutil.cpu_count(logical=False),
                    'memory_gb': psutil.virtual_memory().total / (1024**3),
                    'cpu_freq_mhz': psutil.cpu_freq().max if psutil.cpu_freq() else 2000
                })
            except Exception as e:
                print(f"硬件检测部分失败: {e}")
        
        # 检测GPU
        try:
            import torch
            hardware_info['has_gpu'] = torch.cuda.is_available()
            if hardware_info['has_gpu']:
                hardware_info['gpu_name'] = torch.cuda.get_device_name(0)
        except ImportError:
            pass
        
        return hardware_info
    
    def _calculate_performance_score(self) -> float:
        """计算硬件性能分数 (0-100)"""
        score = 0
        
        # CPU分数 (0-40)
        cpu_score = min(self.hardware_info['cpu_count'] * 5, 40)
        score += cpu_score
        
        # 内存分数 (0-30)
        memory_score = min(self.hardware_info['memory_gb'] * 3, 30)
        score += memory_score
        
        # GPU分数 (0-30)
        if self.hardware_info['has_gpu']:
            score += 30
        
        return min(score, 100)
    
    def recommend_profile(self) -> str:
        """推荐优化配置文件"""
        performance_score = self._calculate_performance_score()
        
        if performance_score >= 80:
            return 'high_quality'
        elif performance_score >= 60:
            return 'balanced'
        elif performance_score >= 40:
            return 'low_latency'
        else:
            return 'ultra_low_latency'
    
    def generate_optimized_config(self, target_profile: str = None) -> Dict[str, Any]:
        """生成优化配置"""
        if target_profile is None:
            target_profile = self.recommend_profile()
        
        profile = self.optimization_profiles[target_profile]
        
        # 基础配置
        config = {
            'camera': {
                'device_id': 0,
                'width': profile['camera_resolution'][0],
                'height': profile['camera_resolution'][1],
                'fps': 30 if profile['target_latency_ms'] > 100 else 60
            },
            'mediapipe': {
                'model_complexity': profile['model_complexity'],
                'min_detection_confidence': 0.3 if profile['target_latency_ms'] < 100 else 0.5,
                'min_tracking_confidence': 0.3 if profile['target_latency_ms'] < 100 else 0.5,
                'enable_segmentation': False,
                'smooth_landmarks': profile['target_latency_ms'] > 100
            },
            'data_processing': {
                'smoothing': {
                    'enabled': profile['enable_filtering'],
                    'method': 'exponential' if profile['target_latency_ms'] < 100 else 'kalman',
                    'alpha': 0.7 if profile['target_latency_ms'] < 100 else 0.3
                },
                'normalization': {
                    'enabled': profile['target_latency_ms'] > 50,
                    'method': 'minmax'
                },
                'selected_keypoints': self._get_keypoints_for_profile(target_profile)
            },
            'mdm': {
                'enabled': profile['enable_prediction'],
                'model_type': 'simple' if profile['target_latency_ms'] < 100 else 'lstm',
                'sequence_length': 3 if profile['target_latency_ms'] < 100 else 10,
                'prediction_length': 1 if profile['target_latency_ms'] < 100 else 5
            },
            'osc': {
                'enabled': True,
                'host': '127.0.0.1',
                'port': 9001,
                'message_format': {
                    'base_address': '/pose',
                    'include_confidence': profile['target_latency_ms'] > 100,
                    'include_velocity': profile['target_latency_ms'] > 100,
                    'include_acceleration': False
                },
                'send_rate': config['camera']['fps'],
                'batch_size': len(self._get_keypoints_for_profile(target_profile))
            },
            'performance': {
                'target_latency_ms': profile['target_latency_ms'],
                'max_queue_size': 2 if profile['target_latency_ms'] < 100 else 10,
                'enable_frame_skip': True,
                'threading': {
                    'capture_thread': True,
                    'processing_thread': True,
                    'osc_thread': True,
                    'max_workers': min(self.hardware_info['cpu_count'], 4)
                }
            },
            'logging': {
                'level': 'WARNING' if profile['target_latency_ms'] < 100 else 'INFO',
                'performance_monitoring': {
                    'enabled': True,
                    'log_interval': 10 if profile['target_latency_ms'] < 100 else 5,
                    'metrics': ['fps', 'latency'] if profile['target_latency_ms'] < 100 else ['fps', 'latency', 'cpu_usage', 'memory_usage']
                }
            },
            'debug': {
                'show_video': False,
                'show_landmarks': False,
                'save_data': False
            }
        }
        
        return config
    
    def _get_keypoints_for_profile(self, profile: str) -> list:
        """根据配置文件获取关键点列表"""
        if profile == 'ultra_low_latency':
            # 只使用最核心的关键点
            return [0, 11, 12, 15, 16, 23, 24]  # 鼻子、肩膀、手腕、臀部
        elif profile == 'low_latency':
            # 核心关键点
            return [0, 11, 12, 13, 14, 15, 16, 23, 24, 25, 26, 27, 28]
        else:
            # 所有关键点
            return list(range(33))
    
    def save_optimized_config(self, config: Dict[str, Any], filename: str):
        """保存优化配置到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            print(f"优化配置已保存到: {filename}")
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def print_hardware_info(self):
        """打印硬件信息"""
        print("硬件信息:")
        print(f"  操作系统: {self.hardware_info['platform']}")
        print(f"  CPU核心数: {self.hardware_info['cpu_count']}")
        print(f"  内存: {self.hardware_info['memory_gb']:.1f}GB")
        if 'cpu_freq_mhz' in self.hardware_info:
            print(f"  CPU频率: {self.hardware_info['cpu_freq_mhz']:.0f}MHz")
        print(f"  GPU支持: {'是' if self.hardware_info['has_gpu'] else '否'}")
        if self.hardware_info['has_gpu'] and 'gpu_name' in self.hardware_info:
            print(f"  GPU型号: {self.hardware_info['gpu_name']}")
        
        performance_score = self._calculate_performance_score()
        print(f"  性能分数: {performance_score:.0f}/100")
    
    def print_profile_comparison(self):
        """打印配置文件对比"""
        print("\n配置文件对比:")
        print(f"{'配置':<20} {'延迟目标':<10} {'分辨率':<12} {'模型复杂度':<10} {'预测':<6} {'滤波':<6}")
        print("-" * 70)
        
        for name, profile in self.optimization_profiles.items():
            resolution = f"{profile['camera_resolution'][0]}x{profile['camera_resolution'][1]}"
            prediction = "是" if profile['enable_prediction'] else "否"
            filtering = "是" if profile['enable_filtering'] else "否"
            
            print(f"{name:<20} {profile['target_latency_ms']:<10}ms {resolution:<12} {profile['model_complexity']:<10} {prediction:<6} {filtering:<6}")
    
    def interactive_optimization(self):
        """交互式优化"""
        print("=" * 60)
        print("系统优化器 - 交互式配置")
        print("=" * 60)
        
        self.print_hardware_info()
        self.print_profile_comparison()
        
        recommended = self.recommend_profile()
        print(f"\n推荐配置: {recommended}")
        
        print("\n请选择优化配置:")
        profiles = list(self.optimization_profiles.keys())
        for i, profile in enumerate(profiles, 1):
            marker = " (推荐)" if profile == recommended else ""
            print(f"{i}. {profile}{marker}")
        
        try:
            choice = input(f"\n请输入选择 (1-{len(profiles)}) 或按回车使用推荐配置: ").strip()
            
            if choice == "":
                selected_profile = recommended
            else:
                selected_profile = profiles[int(choice) - 1]
            
            print(f"\n生成 {selected_profile} 配置...")
            config = self.generate_optimized_config(selected_profile)
            
            filename = f"config/optimized_{selected_profile}.yaml"
            self.save_optimized_config(config, filename)
            
            print(f"\n优化完成！")
            print(f"配置文件: {filename}")
            print(f"启动命令: python run_system.py --config {filename}")
            
        except (ValueError, IndexError):
            print("无效选择，使用推荐配置")
            config = self.generate_optimized_config(recommended)
            filename = f"config/optimized_{recommended}.yaml"
            self.save_optimized_config(config, filename)


def main():
    """主函数"""
    optimizer = SystemOptimizer()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
        optimizer.interactive_optimization()
    else:
        # 自动优化
        print("=" * 60)
        print("系统优化器 - 自动配置")
        print("=" * 60)
        
        optimizer.print_hardware_info()
        
        recommended = optimizer.recommend_profile()
        print(f"\n推荐配置: {recommended}")
        
        config = optimizer.generate_optimized_config(recommended)
        filename = f"config/optimized_{recommended}.yaml"
        optimizer.save_optimized_config(config, filename)
        
        print(f"\n自动优化完成！")
        print(f"配置文件: {filename}")
        print(f"启动命令: python run_system.py --config {filename}")
        print(f"\n如需交互式配置，请运行: python system_optimizer.py --interactive")


if __name__ == "__main__":
    main()
