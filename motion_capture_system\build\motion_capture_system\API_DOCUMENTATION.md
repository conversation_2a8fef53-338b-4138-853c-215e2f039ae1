# 实时动作捕捉和生成系统 API 文档

## 概述

本文档描述了实时动作捕捉和生成系统的API接口和使用方法。

## 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   摄像头输入    │───▶│  MediaPipe检测   │───▶│   数据滤波      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ TouchDesigner   │◀───│   OSC通信模块    │◀───│   动作生成      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 核心模块

### 1. MotionCaptureSystem

主系统类，负责协调所有模块。

#### 初始化

```python
from main import MotionCaptureSystem

system = MotionCaptureSystem(config_path="config/config.yaml")
```

#### 主要方法

- `async start()`: 启动系统
- `async stop()`: 停止系统
- `async initialize_components()`: 初始化所有组件

### 2. PoseDetector

MediaPipe姿态检测器。

#### 初始化

```python
from src.mediapipe_capture.pose_detector import PoseDetector

detector = PoseDetector(camera_config, mediapipe_config)
```

#### 主要方法

- `get_pose() -> Optional[Dict]`: 获取当前帧姿态数据
- `async get_pose_async() -> Optional[Dict]`: 异步获取姿态数据
- `is_camera_opened() -> bool`: 检查摄像头状态
- `cleanup()`: 清理资源

#### 返回数据格式

```python
{
    'landmarks': [
        {
            'id': 0,
            'name': 'nose',
            'x': 320.5,
            'y': 240.3,
            'z': -10.2,
            'visibility': 0.95,
            'normalized_x': 0.5,
            'normalized_y': 0.5,
            'normalized_z': -0.1
        },
        # ... 32 more landmarks
    ],
    'frame_shape': (480, 640, 3),
    'frame_count': 1234,
    'fps': 30.0,
    'timestamp': 1234567890.123,
    'has_pose': True
}
```

### 3. MotionPredictor

动作预测和生成模块。

#### 初始化

```python
from src.mdm_generator.motion_predictor import MotionPredictor

predictor = MotionPredictor(mdm_config)
```

#### 主要方法

- `predict(pose_data: Dict) -> Dict`: 预测动作
- `async predict_async(pose_data: Dict) -> Dict`: 异步预测
- `get_performance_stats() -> Dict`: 获取性能统计
- `reset_buffer()`: 重置输入缓存
- `is_ready() -> bool`: 检查是否准备就绪

#### 增强数据格式

```python
{
    # 原始数据 + 以下字段
    'prediction_applied': True,
    'prediction_confidence': 0.85,
    'predictions': {
        'future_frames': [[...], [...], ...],  # 预测的未来帧
        'prediction_length': 5,
        'model_type': 'lstm'
    }
}
```

### 4. OSCSender

OSC通信模块。

#### 初始化

```python
from src.osc_communication.osc_sender import OSCSender

sender = OSCSender(osc_config)
```

#### 主要方法

- `send_pose_data(pose_data: Dict) -> bool`: 发送姿态数据
- `async send_pose_data_async(pose_data: Dict) -> bool`: 异步发送
- `send_custom_message(address: str, *args) -> bool`: 发送自定义消息
- `get_statistics() -> Dict`: 获取发送统计

#### OSC消息格式

```
/pose/joint_0/x <float>           # 关键点0的X坐标
/pose/joint_0/y <float>           # 关键点0的Y坐标
/pose/joint_0/z <float>           # 关键点0的Z坐标
/pose/joint_0/confidence <float>  # 置信度
/pose/joint_0/name <string>       # 关键点名称

/pose/features/center_x <float>   # 质心X坐标
/pose/features/center_y <float>   # 质心Y坐标
/pose/features/pose_confidence <float>  # 整体姿态置信度

/pose/meta/timestamp <float>      # 时间戳
/pose/meta/fps <float>           # 帧率
/pose/meta/frame_count <int>     # 帧计数
```

### 5. DataFilter

数据滤波器。

#### 初始化

```python
from src.utils.filters import DataFilter

filter = DataFilter(processing_config)
```

#### 主要方法

- `process(pose_data: Dict) -> Dict`: 处理姿态数据
- `reset_filters()`: 重置所有滤波器
- `is_enabled() -> bool`: 检查是否启用
- `set_enabled(enabled: bool)`: 设置启用状态

### 6. PerformanceMonitor

性能监控器。

#### 初始化

```python
from src.utils.performance import PerformanceMonitor

monitor = PerformanceMonitor(monitoring_config)
```

#### 主要方法

- `record_frame(frame_id: str)`: 记录帧
- `start_processing(process_id: str)`: 开始处理计时
- `end_processing(process_id: str)`: 结束处理计时
- `collect_metrics() -> Dict`: 收集性能指标
- `get_performance_summary() -> Dict`: 获取性能摘要

## 配置文件

### 主要配置项

```yaml
# 摄像头设置
camera:
  device_id: 0
  width: 640
  height: 480
  fps: 30

# MediaPipe设置
mediapipe:
  model_complexity: 1
  min_detection_confidence: 0.5
  min_tracking_confidence: 0.5

# 动作生成设置
mdm:
  enabled: true
  model_type: "lstm"  # "lstm", "transformer", "simple"
  
# OSC设置
osc:
  enabled: true
  host: "127.0.0.1"
  port: 9001

# 性能设置
performance:
  target_latency_ms: 100
  max_queue_size: 10
  enable_frame_skip: true
```

## 使用示例

### 基本使用

```python
import asyncio
from main import MotionCaptureSystem

async def main():
    system = MotionCaptureSystem()
    await system.start()

if __name__ == "__main__":
    asyncio.run(main())
```

### 自定义配置

```python
import yaml
from main import MotionCaptureSystem

# 修改配置
config = {
    'camera': {'device_id': 1, 'fps': 60},
    'osc': {'port': 9002},
    'mdm': {'model_type': 'transformer'}
}

# 保存配置
with open('custom_config.yaml', 'w') as f:
    yaml.dump(config, f)

# 使用自定义配置
system = MotionCaptureSystem('custom_config.yaml')
```

### 单独使用模块

```python
# 只使用姿态检测
from src.mediapipe_capture.pose_detector import PoseDetector

detector = PoseDetector(camera_config, mediapipe_config)
while True:
    pose_data = detector.get_pose()
    if pose_data and pose_data['has_pose']:
        print(f"检测到 {len(pose_data['landmarks'])} 个关键点")
```

## 错误处理

### 常见错误类型

- `CAMERA_ERROR`: 摄像头相关错误
- `PROCESSING_ERROR`: 数据处理错误
- `NETWORK_ERROR`: 网络通信错误
- `MODEL_ERROR`: 模型相关错误

### 错误处理示例

```python
from src.utils.error_handler import ErrorHandler, ErrorType, ErrorSeverity

error_handler = ErrorHandler()

try:
    # 执行操作
    result = some_operation()
except Exception as e:
    error_handler.handle_error(
        error_type=ErrorType.PROCESSING_ERROR,
        severity=ErrorSeverity.HIGH,
        message="操作失败",
        exception=e
    )
```

## 性能优化

### 延迟优化建议

1. **降低分辨率**: 减少摄像头分辨率
2. **调整模型复杂度**: 使用较低的MediaPipe模型复杂度
3. **启用帧跳跃**: 在配置中启用`enable_frame_skip`
4. **选择合适的模型**: 使用`simple`模型获得最低延迟

### 内存优化建议

1. **限制队列大小**: 调整`max_queue_size`
2. **定期清理**: 启用自动资源清理
3. **监控内存使用**: 使用性能监控器

## 扩展开发

### 添加新的滤波算法

```python
from src.utils.filters import DataFilter

class CustomFilter:
    def update(self, value):
        # 自定义滤波逻辑
        return filtered_value

# 在DataFilter中注册新滤波器
```

### 添加新的动作生成模型

```python
from src.mdm_generator.model import create_model

class CustomModel:
    def predict_sequence(self, input_sequence):
        # 自定义预测逻辑
        return predictions

# 在create_model函数中添加新模型类型
```

### 自定义OSC消息格式

```python
from src.osc_communication.osc_sender import OSCSender

class CustomOSCSender(OSCSender):
    def _send_landmarks(self, landmarks, pose_data):
        # 自定义消息格式
        for landmark in landmarks:
            custom_address = f"/custom/{landmark['name']}"
            self.client.send_message(custom_address, [landmark['x'], landmark['y']])
```

## 故障排除

### 常见问题

1. **摄像头无法打开**
   - 检查设备ID是否正确
   - 确认摄像头未被其他程序占用

2. **OSC连接失败**
   - 检查网络连接
   - 确认接收端端口设置

3. **性能问题**
   - 检查CPU和内存使用率
   - 调整配置参数

### 调试模式

```python
# 启用调试模式
config['logging']['level'] = 'DEBUG'
config['debug']['show_video'] = True
config['debug']['show_landmarks'] = True
```

## API参考

详细的API参考请查看各模块的源代码注释和类型提示。
