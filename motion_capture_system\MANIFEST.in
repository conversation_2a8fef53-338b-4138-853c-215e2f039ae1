# MANIFEST.in - 指定要包含在分发包中的文件

# 包含文档文件
include README.md
include LICENSE
include CHANGELOG.md
include API_DOCUMENTATION.md
include QUICK_START.md
include INSTALLATION_REPORT.md

# 包含配置文件
include requirements.txt
include setup.py
include pyproject.toml

# 包含配置目录
recursive-include config *.yaml *.yml *.json

# 包含示例代码
recursive-include examples *.py *.md *.txt *.yaml *.yml

# 包含测试文件
recursive-include tests *.py

# 包含源代码
recursive-include src *.py

# 包含脚本文件
include *.py
include *.bat
include *.sh

# 排除不需要的文件
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .pytest_cache
global-exclude *.log

# 排除构建和分发目录
prune build
prune dist
prune venv
prune motion_env
prune logs
prune data
prune models
prune temp

# 排除IDE文件
global-exclude .vscode
global-exclude .idea
global-exclude *.swp
global-exclude *.swo
global-exclude *~

# 排除系统文件
global-exclude .DS_Store
global-exclude Thumbs.db
