# 🎭 实时动作捕捉和生成系统

一个基于MediaPipe和深度学习的实时人体动作捕捉、预测和生成系统，支持OSC通信协议，可与TouchDesigner等创意软件无缝集成。

## ✨ 主要特性

- **🎯 实时姿态检测**: 使用MediaPipe进行高精度人体姿态检测 (33个关键点)
- **🧠 智能动作预测**: 基于LSTM/Transformer的动作预测和生成
- **⚡ 超低延迟通信**: 优化的OSC通信协议，延迟<30ms
- **🔧 多种滤波算法**: 卡尔曼滤波、移动平均、指数平滑等
- **📊 性能监控**: 实时FPS、延迟和资源使用监控
- **🛡️ 错误处理**: 完善的异常处理和自动恢复机制
- **🎨 TouchDesigner集成**: 完整的创意软件集成支持
- **⚙️ 自动优化**: 根据硬件自动生成最优配置

## 🏗️ 系统架构

```
motion_capture_system/
├── src/                          # 📁 源代码
│   ├── mdm_generator/           # 🤖 动作生成模块
│   │   ├── __init__.py
│   │   ├── model.py             # 深度学习模型
│   │   ├── motion_predictor.py  # 动作预测器
│   │   └── transformer.py       # Transformer模型
│   ├── mediapipe_capture/       # 📷 MediaPipe捕捉模块
│   ├── osc_communication/       # 📡 OSC通信模块
│   │   ├── __init__.py
│   │   └── osc_sender.py        # OSC发送器
│   └── utils/                   # 🔧 工具函数
├── config/                      # ⚙️ 配置文件
│   ├── config.yaml              # 默认配置
│   ├── optimized_ultra_low_latency.yaml  # 超低延迟配置
│   └── performance_config.yaml  # 高性能配置
├── examples/                    # 📚 示例代码
│   ├── touchdesigner_integration.py
│   └── simple_visualizer.py
├── tests/                       # 🧪 测试文件
├── logs/                        # 📝 日志文件
├── demo.py                      # 🎬 演示程序
├── main.py                      # 🚀 主程序
├── run_system.py               # 🏃 系统启动器
├── system_optimizer.py         # 🔧 系统优化器
├── benchmark_performance.py    # 📊 性能基准测试
└── requirements.txt            # 📦 依赖列表
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv myenv

# 激活虚拟环境
# Windows:
myenv\Scripts\activate
# Linux/Mac:
source myenv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 自动优化配置

```bash
# 运行系统优化器，自动生成最优配置
python system_optimizer.py
```

### 3. 基础使用

```bash
# 运行主程序 (使用默认配置)
python main.py

# 使用优化配置
python run_system.py --config config/optimized_ultra_low_latency.yaml

# 运行交互式演示程序
python demo.py

# 运行性能基准测试
python benchmark_performance.py
```

### 4. 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行基础功能测试
python tests/test_basic_functionality.py
```

## 📋 配置说明

系统提供多种预设配置，可根据不同需求选择：

### 🎯 配置类型

| 配置文件 | 用途 | 延迟 | 分辨率 | 适用场景 |
|---------|------|------|--------|----------|
| `config.yaml` | 默认配置 | ~50ms | 640x480 | 一般应用 |
| `optimized_ultra_low_latency.yaml` | 超低延迟 | <30ms | 160x120 | 实时交互 |
| `performance_config.yaml` | 高性能 | ~80ms | 640x480 | 专业应用 |

### ⚙️ 主要配置参数

```yaml
# 摄像头配置
camera:
  device_id: 0        # 摄像头设备ID
  fps: 30            # 帧率
  width: 640         # 分辨率宽度
  height: 480        # 分辨率高度

# MediaPipe配置
mediapipe:
  model_complexity: 1              # 模型复杂度 (0-2)
  min_detection_confidence: 0.5    # 检测置信度阈值
  min_tracking_confidence: 0.5     # 跟踪置信度阈值
  smooth_landmarks: true           # 启用关键点平滑

# OSC通信配置
osc:
  enabled: true       # 启用OSC通信
  host: "127.0.0.1"  # OSC服务器地址
  port: 9001         # OSC端口
  send_rate: 30      # 发送频率 (Hz)
  batch_size: 7      # 批量发送大小

# 性能配置
performance:
  target_latency_ms: 50    # 目标延迟 (毫秒)
  max_queue_size: 5        # 最大队列大小
  enable_frame_skip: false # 启用帧跳过
  threading:
    capture_thread: true   # 启用捕捉线程
    processing_thread: true # 启用处理线程
    osc_thread: true       # 启用OSC线程
    max_workers: 2         # 最大工作线程数
```

## 🎨 TouchDesigner集成

### 📡 OSC数据格式

系统发送以下OSC消息到TouchDesigner：

```
# 关键点位置数据 (33个关键点)
/pose/joint_0/x         # 鼻子X坐标
/pose/joint_0/y         # 鼻子Y坐标
/pose/joint_0/z         # 鼻子Z坐标
/pose/joint_0/norm_x    # 归一化X坐标 (0-1)
/pose/joint_0/norm_y    # 归一化Y坐标 (0-1)
/pose/joint_0/norm_z    # 归一化Z坐标 (0-1)
/pose/joint_0/confidence # 置信度 (0-1)
...
/pose/joint_32/x        # 右脚小趾X坐标

# 特殊数据
/pose/center/x          # 身体质心X
/pose/center/y          # 身体质心Y
/pose/center/z          # 身体质心Z
/pose/bbox/width        # 边界框宽度
/pose/bbox/height       # 边界框高度

# 关键关节快速访问
/pose/key/head/x        # 头部位置
/pose/key/left_hand/x   # 左手位置
/pose/key/right_hand/x  # 右手位置

# 元数据
/pose/meta/timestamp    # 时间戳
/pose/meta/frame_count  # 帧计数
/pose/meta/fps          # 当前FPS

# 控制信号
/control/beat           # 节拍信号 (0-3)
/control/cycle          # 周期信号 (-1 to 1)
/control/trigger        # 随机触发 (0 or 1)
```

### 🎛️ TouchDesigner设置

1. **添加OSC接收器**
   - 添加 `OSC In CHOP`
   - 设置网络地址: `127.0.0.1`
   - 设置端口: `9001`
   - 启用 `Active`

2. **数据处理**
   - 使用 `Select CHOP` 过滤需要的通道
   - 使用 `Lag CHOP` 平滑数据
   - 使用 `Math CHOP` 进行数据变换

详细设置请参考: `examples/TouchDesigner_Setup_Guide.md`

### 🎬 运行TouchDesigner集成演示

```bash
# 运行TouchDesigner集成示例
python examples/touchdesigner_integration.py
```

## 📊 性能优化

### ⚡ 超低延迟模式

对于实时交互应用，使用超低延迟配置：

```bash
python run_system.py --config config/optimized_ultra_low_latency.yaml
```

**优化策略:**
- 🔽 降低分辨率 (160x120)
- 🎯 简化模型复杂度 (complexity=0)
- 📉 减少关键点数量 (7个核心关键点)
- 🚫 禁用数据平滑和滤波
- ⏭️ 启用智能帧跳过

**性能指标:**
- 延迟: <30ms
- FPS: 60+
- CPU使用率: <50%

### 🚀 高性能模式

对于专业应用，使用高性能配置：

```bash
python run_system.py --config config/performance_config.yaml
```

**功能特性:**
- 🎯 高分辨率 (640x480)
- 📍 完整关键点检测 (33个关键点)
- 🧠 启用动作预测和生成
- 🧵 多线程并行处理
- 🔧 高级滤波算法

### 📈 性能基准测试

```bash
# 运行完整性能测试
python benchmark_performance.py

# 测试特定配置
python benchmark_performance.py --config config/optimized_ultra_low_latency.yaml
```

## 🔧 故障排除

### ❓ 常见问题

#### 1. 摄像头问题
```bash
# 问题: 摄像头无法打开
# 解决方案:
- 检查摄像头连接和驱动
- 确认设备ID正确 (通常是0)
- 关闭其他使用摄像头的程序

# 测试摄像头
python -c "import cv2; cap = cv2.VideoCapture(0); print('摄像头可用' if cap.isOpened() else '摄像头不可用'); cap.release()"
```

#### 2. OSC通信问题
```bash
# 问题: OSC连接失败
# 解决方案:
- 检查端口9001是否被占用
- 确认防火墙设置允许UDP通信
- 验证TouchDesigner OSC In设置

# 测试OSC连接
python -c "from pythonosc import udp_client; client = udp_client.SimpleUDPClient('127.0.0.1', 9001); client.send_message('/test', 1.0); print('OSC测试消息已发送')"
```

#### 3. 性能问题
```bash
# 问题: FPS低或延迟高
# 解决方案:
- 使用超低延迟配置
- 降低分辨率和帧率
- 关闭不必要的功能

# 运行性能诊断
python benchmark_performance.py --diagnose
```

### 🐛 调试模式

启用详细调试信息：

```bash
# 启用调试模式
python main.py --debug --log-level DEBUG

# 保存调试数据
python main.py --debug --save-data

# 显示实时视频窗口
python main.py --debug --show-video --show-landmarks
```

## 👨‍💻 开发指南

### 🧪 测试框架

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试文件
python -m pytest tests/test_basic_functionality.py -v

# 生成覆盖率报告
python -m pytest --cov=src --cov-report=html tests/
```

### 📝 代码规范

项目遵循以下代码规范：

- **PEP 8**: Python代码风格指南
- **Type Hints**: 使用类型注解提高代码可读性
- **Docstrings**: 所有公共函数都有文档字符串
- **Async/Await**: 异步操作使用现代语法

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/amazing-feature`)
3. **提交更改** (`git commit -m 'Add amazing feature'`)
4. **推送到分支** (`git push origin feature/amazing-feature`)
5. **创建 Pull Request**

## 📈 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🎯 基础动作捕捉功能
- 📡 OSC通信支持
- 🎨 TouchDesigner集成
- 🔧 自动系统优化
- 📊 性能监控和基准测试

### v1.1.0 (计划中)
- 🧠 高级动作预测功能
- 🔧 更多滤波算法选项
- ⚡ 进一步性能优化
- 🌐 Web管理界面

## 📞 联系方式

- 📧 邮箱: [<EMAIL>]
- 🐙 GitHub: [https://github.com/your-username/motion-capture-system]
- 📋 Issues: [https://github.com/your-username/motion-capture-system/issues]

---

<div align="center">

**🎭 让动作捕捉变得简单而强大！**

[![Stars](https://img.shields.io/github/stars/your-username/motion-capture-system?style=social)](https://github.com/your-username/motion-capture-system/stargazers)
[![License](https://img.shields.io/github/license/your-username/motion-capture-system)](https://github.com/your-username/motion-capture-system/blob/main/LICENSE)

</div>
