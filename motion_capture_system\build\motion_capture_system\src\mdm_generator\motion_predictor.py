"""
动作预测器
整合各种动作生成模型，提供统一的预测接口
"""

import asyncio
import time
from collections import deque
from typing import Dict, List, Optional, Any, Union
import numpy as np
import torch
from loguru import logger

from .model import LSTMMotionModel, SimpleMotionModel, MotionEnhancer, create_model
from .transformer import MotionTransformer
from ..mediapipe_capture.data_processor import DataProcessor


class MotionPredictor:
    """动作预测器主类"""
    
    def __init__(self, config: Dict):
        """
        初始化动作预测器
        
        Args:
            config: 预测器配置
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.model_type = config.get('model_type', 'simple')
        
        # 初始化模型
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 数据缓存
        self.sequence_length = config.get('sequence_length', 10)
        self.prediction_length = config.get('prediction_length', 5)
        self.input_buffer = deque(maxlen=self.sequence_length)
        
        # 动作增强器
        self.enhancer = MotionEnhancer()
        
        # 性能统计
        self.prediction_count = 0
        self.total_prediction_time = 0.0
        
        if self.enabled:
            self._initialize_model()
        
        logger.info(f"动作预测器初始化完成，模型类型: {self.model_type}")
    
    def _initialize_model(self):
        """初始化预测模型"""
        try:
            if self.model_type == 'lstm':
                self.model = create_model('lstm', self.config)
                self.model.to(self.device)
                
                # 加载预训练模型
                model_path = self.config.get('model_path')
                if model_path:
                    self._load_model(model_path)
                
            elif self.model_type == 'transformer':
                self.model = MotionTransformer(self.config)
                
            elif self.model_type == 'simple':
                self.model = create_model('simple', self.config)
                
            else:
                logger.warning(f"未知的模型类型: {self.model_type}，使用简单模型")
                self.model = create_model('simple', self.config)
                self.model_type = 'simple'
            
            logger.info(f"模型初始化成功: {self.model_type}")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            # 回退到简单模型
            self.model = create_model('simple', self.config)
            self.model_type = 'simple'
    
    def predict(self, pose_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预测动作
        
        Args:
            pose_data: 输入姿态数据
            
        Returns:
            增强后的姿态数据
        """
        if not self.enabled or not pose_data or not pose_data.get('has_pose'):
            return pose_data
        
        start_time = time.time()
        
        try:
            # 提取特征向量
            feature_vector = self._extract_features(pose_data)
            
            if feature_vector is None:
                return pose_data
            
            # 添加到输入缓存
            self.input_buffer.append(feature_vector)
            
            # 如果缓存不够，返回原始数据
            if len(self.input_buffer) < self.sequence_length:
                return pose_data
            
            # 执行预测
            predictions = self._predict_sequence()
            
            # 应用预测结果
            enhanced_data = self._apply_predictions(pose_data, predictions)
            
            # 更新统计信息
            self.prediction_count += 1
            self.total_prediction_time += time.time() - start_time
            
            return enhanced_data
            
        except Exception as e:
            logger.error(f"动作预测失败: {e}")
            return pose_data
    
    async def predict_async(self, pose_data: Dict[str, Any]) -> Dict[str, Any]:
        """异步预测动作"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.predict, pose_data)
    
    def _extract_features(self, pose_data: Dict[str, Any]) -> Optional[np.ndarray]:
        """
        从姿态数据中提取特征向量
        
        Args:
            pose_data: 姿态数据
            
        Returns:
            特征向量
        """
        landmarks = pose_data.get('landmarks')
        if not landmarks:
            return None
        
        # 提取坐标特征
        features = []
        for landmark in landmarks:
            if landmark['visibility'] > 0.5:
                features.extend([
                    landmark.get('normalized_x', landmark['x']),
                    landmark.get('normalized_y', landmark['y']),
                    landmark.get('normalized_z', landmark['z'])
                ])
            else:
                features.extend([0.0, 0.0, 0.0])
        
        return np.array(features, dtype=np.float32)
    
    def _predict_sequence(self) -> Optional[np.ndarray]:
        """
        预测动作序列
        
        Returns:
            预测的动作序列
        """
        if len(self.input_buffer) < self.sequence_length:
            return None
        
        # 准备输入序列
        input_sequence = np.array(list(self.input_buffer))
        
        try:
            if self.model_type == 'lstm':
                return self._predict_with_lstm(input_sequence)
            elif self.model_type == 'transformer':
                return self._predict_with_transformer(input_sequence)
            elif self.model_type == 'simple':
                return self._predict_with_simple_model(input_sequence)
            else:
                return None
                
        except Exception as e:
            logger.error(f"序列预测失败: {e}")
            return None
    
    def _predict_with_lstm(self, input_sequence: np.ndarray) -> np.ndarray:
        """使用LSTM模型预测"""
        self.model.eval()
        with torch.no_grad():
            # 转换为张量
            input_tensor = torch.FloatTensor(input_sequence).unsqueeze(0).to(self.device)
            
            # 预测
            predictions = self.model.predict_sequence(input_tensor)
            
            # 转换回numpy
            return predictions.cpu().numpy().squeeze(0)
    
    def _predict_with_transformer(self, input_sequence: np.ndarray) -> np.ndarray:
        """使用Transformer模型预测"""
        return self.model.predict(input_sequence)
    
    def _predict_with_simple_model(self, input_sequence: np.ndarray) -> np.ndarray:
        """使用简单模型预测"""
        # 使用线性外推
        return self.model.predict_linear_extrapolation(input_sequence)
    
    def _apply_predictions(self, pose_data: Dict[str, Any], predictions: Optional[np.ndarray]) -> Dict[str, Any]:
        """
        应用预测结果到姿态数据
        
        Args:
            pose_data: 原始姿态数据
            predictions: 预测结果
            
        Returns:
            增强后的姿态数据
        """
        enhanced_data = pose_data.copy()
        
        if predictions is None or len(predictions) == 0:
            enhanced_data['prediction_applied'] = False
            return enhanced_data
        
        # 使用第一个预测帧来增强当前帧
        next_frame_prediction = predictions[0]
        
        # 重构关键点数据
        landmarks = pose_data.get('landmarks', [])
        if len(landmarks) * 3 == len(next_frame_prediction):
            enhanced_landmarks = []
            
            for i, landmark in enumerate(landmarks):
                enhanced_landmark = landmark.copy()
                
                # 如果置信度较低，使用预测值
                if landmark['visibility'] < 0.7:
                    base_idx = i * 3
                    enhanced_landmark['x'] = next_frame_prediction[base_idx]
                    enhanced_landmark['y'] = next_frame_prediction[base_idx + 1]
                    enhanced_landmark['z'] = next_frame_prediction[base_idx + 2]
                    enhanced_landmark['predicted'] = True
                else:
                    # 高置信度的关键点进行轻微平滑
                    base_idx = i * 3
                    alpha = 0.1  # 平滑系数
                    enhanced_landmark['x'] = (1 - alpha) * landmark['x'] + alpha * next_frame_prediction[base_idx]
                    enhanced_landmark['y'] = (1 - alpha) * landmark['y'] + alpha * next_frame_prediction[base_idx + 1]
                    enhanced_landmark['z'] = (1 - alpha) * landmark['z'] + alpha * next_frame_prediction[base_idx + 2]
                    enhanced_landmark['predicted'] = False
                
                enhanced_landmarks.append(enhanced_landmark)
            
            enhanced_data['landmarks'] = enhanced_landmarks
            enhanced_data['prediction_applied'] = True
            enhanced_data['prediction_confidence'] = self._calculate_prediction_confidence(predictions)
        else:
            enhanced_data['prediction_applied'] = False
        
        # 添加预测元数据
        enhanced_data['predictions'] = {
            'future_frames': predictions.tolist() if predictions is not None else [],
            'prediction_length': self.prediction_length,
            'model_type': self.model_type
        }
        
        return enhanced_data

    def _calculate_prediction_confidence(self, predictions: np.ndarray) -> float:
        """
        计算预测置信度

        Args:
            predictions: 预测序列

        Returns:
            置信度分数 (0-1)
        """
        if predictions is None or len(predictions) == 0:
            return 0.0

        # 基于预测序列的稳定性计算置信度
        if len(predictions) > 1:
            # 计算相邻帧之间的变化
            frame_diffs = np.diff(predictions, axis=0)
            avg_change = np.mean(np.abs(frame_diffs))

            # 变化越小，置信度越高
            confidence = max(0.0, min(1.0, 1.0 - avg_change * 10))
        else:
            confidence = 0.8  # 单帧预测的默认置信度

        return confidence

    def _load_model(self, model_path: str):
        """
        加载预训练模型

        Args:
            model_path: 模型文件路径
        """
        try:
            if self.model_type in ['lstm', 'transformer']:
                checkpoint = torch.load(model_path, map_location=self.device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"模型加载成功: {model_path}")
            else:
                logger.warning(f"模型类型 {self.model_type} 不支持加载预训练模型")
        except Exception as e:
            logger.warning(f"模型加载失败: {e}")

    def save_model(self, model_path: str):
        """
        保存模型

        Args:
            model_path: 模型保存路径
        """
        try:
            if self.model_type in ['lstm', 'transformer']:
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'config': self.config
                }, model_path)
                logger.info(f"模型保存成功: {model_path}")
            else:
                logger.warning(f"模型类型 {self.model_type} 不支持保存")
        except Exception as e:
            logger.error(f"模型保存失败: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            性能统计字典
        """
        if self.prediction_count == 0:
            return {
                'prediction_count': 0,
                'average_prediction_time_ms': 0.0,
                'predictions_per_second': 0.0
            }

        avg_time = self.total_prediction_time / self.prediction_count

        return {
            'prediction_count': self.prediction_count,
            'average_prediction_time_ms': avg_time * 1000,
            'predictions_per_second': 1.0 / avg_time if avg_time > 0 else 0.0,
            'model_type': self.model_type,
            'enabled': self.enabled
        }

    def reset_buffer(self):
        """重置输入缓存"""
        self.input_buffer.clear()
        logger.info("动作预测器缓存已重置")

    def set_enabled(self, enabled: bool):
        """
        设置预测器启用状态

        Args:
            enabled: 是否启用
        """
        self.enabled = enabled
        if not enabled:
            self.reset_buffer()
        logger.info(f"动作预测器{'启用' if enabled else '禁用'}")

    def is_ready(self) -> bool:
        """
        检查预测器是否准备就绪

        Returns:
            是否准备就绪
        """
        return (self.enabled and
                self.model is not None and
                len(self.input_buffer) >= self.sequence_length)

    def get_buffer_status(self) -> Dict[str, Any]:
        """
        获取缓存状态

        Returns:
            缓存状态字典
        """
        return {
            'buffer_size': len(self.input_buffer),
            'max_size': self.sequence_length,
            'fill_percentage': len(self.input_buffer) / self.sequence_length * 100,
            'is_ready': self.is_ready()
        }
