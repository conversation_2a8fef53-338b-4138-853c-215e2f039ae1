# 部署摘要

## 构建信息
- 版本: v1.0.0
- 构建时间: 2025-07-22 16:19:40
- Python版本: 3.12.9
- 平台: win32

## 包信息
- 文件名: motion_capture_system_v1.0.0.zip
- 大小: 0.1 MB
- 路径: D:\software_code\j07129\motion_capture_system\dist\motion_capture_system_v1.0.0.zip

## 安装说明
1. 解压ZIP文件到目标目录
2. Windows用户: 双击 install_windows.bat
3. Linux/Mac用户: 运行 ./install_unix.sh
4. 或手动运行: python install.py

## 快速开始
1. python system_optimizer.py  # 系统优化
2. python main.py             # 启动系统
3. python demo.py             # 运行演示

## 文件结构
motion_capture_system/
├── src/                      # 源代码
├── config/                   # 配置文件
├── examples/                 # 示例代码
├── tests/                    # 测试文件
├── install.py               # 安装脚本
├── install_windows.bat      # Windows安装器
├── install_unix.sh          # Unix安装器
├── QUICK_START.md           # 快速开始指南
├── README.md                # 完整文档
└── deployment_info.json     # 部署信息

## 支持
- GitHub: https://github.com/your-username/motion-capture-system
- Issues: https://github.com/your-username/motion-capture-system/issues
- 文档: README.md
