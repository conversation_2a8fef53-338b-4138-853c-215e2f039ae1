# 🚀 快速开始指南

## 系统信息
- 版本: v1.0.0
- 构建日期: 2025-07-22 16:19:40

## 🔧 安装步骤

### 1. 系统要求
- Python 3.8 或更高版本
- 4GB 或更多内存
- USB 摄像头
- 网络连接 (用于OSC通信)

### 2. 自动安装
```bash
# 运行自动安装脚本
python install.py
```

### 3. 手动安装 (可选)
```bash
# 创建虚拟环境
python -m venv motion_env

# 激活虚拟环境
# Windows:
motion_env\Scripts\activate
# Linux/Mac:
source motion_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

## 🎬 快速使用

### 1. 系统优化
```bash
# 自动生成最优配置
python system_optimizer.py
```

### 2. 启动系统
```bash
# 使用默认配置
python main.py

# 使用优化配置
python run_system.py --config config/optimized_ultra_low_latency.yaml
```

### 3. 运行演示
```bash
# 交互式演示程序
python demo.py
```

### 4. 性能测试
```bash
# 运行性能基准测试
python benchmark_performance.py
```

## 🎨 TouchDesigner 集成

### OSC 设置
1. 在 TouchDesigner 中添加 `OSC In CHOP`
2. 设置网络地址: `127.0.0.1`
3. 设置端口: `9001`
4. 启用 `Active`

### 数据格式
系统发送以下 OSC 消息:
- `/pose/joint_N/x,y,z` - 关键点位置
- `/pose/center/x,y,z` - 身体质心
- `/pose/meta/fps` - 当前帧率
- `/control/beat` - 节拍信号

详细信息请参考 `examples/TouchDesigner_Setup_Guide.md`

## 🔧 配置说明

### 配置文件
- `config/config.yaml` - 默认配置
- `config/optimized_ultra_low_latency.yaml` - 超低延迟配置
- `config/performance_config.yaml` - 高性能配置

### 主要参数
```yaml
camera:
  device_id: 0      # 摄像头设备ID
  fps: 30          # 帧率
  width: 640       # 分辨率宽度
  height: 480      # 分辨率高度

osc:
  enabled: true    # 启用OSC通信
  host: "127.0.0.1" # OSC服务器地址
  port: 9001       # OSC端口

performance:
  target_latency_ms: 50  # 目标延迟
  enable_frame_skip: false # 启用帧跳过
```

## 🐛 故障排除

### 常见问题

1. **摄像头无法打开**
   - 检查摄像头连接
   - 确认设备ID正确
   - 关闭其他使用摄像头的程序

2. **OSC连接失败**
   - 检查端口是否被占用
   - 确认防火墙设置
   - 验证TouchDesigner设置

3. **性能问题**
   - 使用低延迟配置
   - 降低分辨率和帧率
   - 关闭不必要的功能

### 调试模式
```bash
# 启用调试模式
python main.py --debug --log-level DEBUG

# 显示实时视频
python main.py --debug --show-video
```

## 📚 更多资源

- 完整文档: `README.md`
- API文档: `API_DOCUMENTATION.md`
- 示例代码: `examples/` 目录
- 测试用例: `tests/` 目录

## 📞 支持

如有问题，请查看:
- GitHub Issues: https://github.com/your-repo/issues
- 文档: README.md
- 日志文件: logs/ 目录

---

🎭 **祝您使用愉快！**
