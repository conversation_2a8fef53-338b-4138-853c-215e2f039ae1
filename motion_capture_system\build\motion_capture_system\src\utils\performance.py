"""
性能监控器
提供系统性能监控和指标收集功能
"""

import time
import threading
from collections import deque
from typing import Dict, List, Optional, Any
import psutil
import numpy as np
from loguru import logger


class PerformanceMonitor:
    """性能监控器类"""
    
    def __init__(self, config: Dict):
        """
        初始化性能监控器
        
        Args:
            config: 监控配置
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.log_interval = config.get('log_interval', 5)
        self.metrics = config.get('metrics', ['fps', 'latency', 'cpu_usage', 'memory_usage'])
        
        # 性能数据缓存
        self.fps_history = deque(maxlen=100)
        self.latency_history = deque(maxlen=100)
        self.cpu_history = deque(maxlen=100)
        self.memory_history = deque(maxlen=100)
        
        # 时间戳记录
        self.frame_timestamps = deque(maxlen=100)
        self.processing_start_times = {}
        self.processing_end_times = {}
        
        # 统计数据
        self.total_frames = 0
        self.dropped_frames = 0
        self.start_time = time.time()
        
        # 线程锁
        self.lock = threading.Lock()
        
        logger.info("性能监控器初始化完成")
    
    def record_frame(self, frame_id: Optional[str] = None):
        """
        记录帧时间戳
        
        Args:
            frame_id: 帧ID（可选）
        """
        if not self.enabled:
            return
        
        with self.lock:
            current_time = time.time()
            self.frame_timestamps.append(current_time)
            self.total_frames += 1
            
            # 计算FPS
            if len(self.frame_timestamps) >= 2:
                time_diff = current_time - self.frame_timestamps[0]
                if time_diff > 0:
                    fps = len(self.frame_timestamps) / time_diff
                    self.fps_history.append(fps)
    
    def start_processing(self, process_id: str):
        """
        开始处理计时
        
        Args:
            process_id: 处理过程ID
        """
        if not self.enabled:
            return
        
        with self.lock:
            self.processing_start_times[process_id] = time.time()
    
    def end_processing(self, process_id: str):
        """
        结束处理计时
        
        Args:
            process_id: 处理过程ID
        """
        if not self.enabled:
            return
        
        with self.lock:
            if process_id in self.processing_start_times:
                start_time = self.processing_start_times[process_id]
                end_time = time.time()
                latency = (end_time - start_time) * 1000  # 转换为毫秒
                
                self.latency_history.append(latency)
                self.processing_end_times[process_id] = end_time
                
                # 清理开始时间记录
                del self.processing_start_times[process_id]
    
    def record_dropped_frame(self):
        """记录丢帧"""
        if not self.enabled:
            return
        
        with self.lock:
            self.dropped_frames += 1
    
    def collect_metrics(self) -> Dict[str, Any]:
        """
        收集性能指标
        
        Returns:
            性能指标字典
        """
        if not self.enabled:
            return {}
        
        metrics = {}
        
        with self.lock:
            # FPS指标
            if 'fps' in self.metrics and self.fps_history:
                metrics['fps'] = {
                    'current': self.fps_history[-1] if self.fps_history else 0,
                    'average': np.mean(self.fps_history),
                    'min': np.min(self.fps_history),
                    'max': np.max(self.fps_history),
                    'std': np.std(self.fps_history)
                }
            
            # 延迟指标
            if 'latency' in self.metrics and self.latency_history:
                metrics['latency_ms'] = {
                    'current': self.latency_history[-1] if self.latency_history else 0,
                    'average': np.mean(self.latency_history),
                    'min': np.min(self.latency_history),
                    'max': np.max(self.latency_history),
                    'std': np.std(self.latency_history),
                    'p95': np.percentile(self.latency_history, 95),
                    'p99': np.percentile(self.latency_history, 99)
                }
            
            # 帧统计
            metrics['frame_stats'] = {
                'total_frames': self.total_frames,
                'dropped_frames': self.dropped_frames,
                'drop_rate': self.dropped_frames / max(self.total_frames, 1),
                'uptime_seconds': time.time() - self.start_time
            }
        
        # CPU使用率
        if 'cpu_usage' in self.metrics:
            try:
                cpu_percent = psutil.cpu_percent(interval=None)
                self.cpu_history.append(cpu_percent)
                metrics['cpu_usage'] = {
                    'current': cpu_percent,
                    'average': np.mean(self.cpu_history) if self.cpu_history else 0
                }
            except Exception as e:
                logger.warning(f"CPU使用率获取失败: {e}")
        
        # 内存使用率
        if 'memory_usage' in self.metrics:
            try:
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                memory_mb = memory.used / (1024 * 1024)
                
                self.memory_history.append(memory_percent)
                metrics['memory_usage'] = {
                    'percent': memory_percent,
                    'used_mb': memory_mb,
                    'available_mb': memory.available / (1024 * 1024),
                    'average_percent': np.mean(self.memory_history) if self.memory_history else 0
                }
            except Exception as e:
                logger.warning(f"内存使用率获取失败: {e}")
        
        return metrics
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Returns:
            性能摘要字典
        """
        metrics = self.collect_metrics()
        
        summary = {
            'status': 'healthy',
            'warnings': [],
            'errors': []
        }
        
        # 检查FPS
        if 'fps' in metrics:
            fps_avg = metrics['fps']['average']
            if fps_avg < 15:
                summary['status'] = 'warning'
                summary['warnings'].append(f"平均FPS过低: {fps_avg:.1f}")
            elif fps_avg < 10:
                summary['status'] = 'error'
                summary['errors'].append(f"平均FPS严重过低: {fps_avg:.1f}")
        
        # 检查延迟
        if 'latency_ms' in metrics:
            latency_avg = metrics['latency_ms']['average']
            latency_p95 = metrics['latency_ms']['p95']
            
            if latency_avg > 100:
                summary['status'] = 'warning'
                summary['warnings'].append(f"平均延迟过高: {latency_avg:.1f}ms")
            
            if latency_p95 > 200:
                summary['status'] = 'error'
                summary['errors'].append(f"P95延迟过高: {latency_p95:.1f}ms")
        
        # 检查丢帧率
        if 'frame_stats' in metrics:
            drop_rate = metrics['frame_stats']['drop_rate']
            if drop_rate > 0.05:  # 5%
                summary['status'] = 'warning'
                summary['warnings'].append(f"丢帧率过高: {drop_rate*100:.1f}%")
            elif drop_rate > 0.1:  # 10%
                summary['status'] = 'error'
                summary['errors'].append(f"丢帧率严重过高: {drop_rate*100:.1f}%")
        
        # 检查CPU使用率
        if 'cpu_usage' in metrics:
            cpu_current = metrics['cpu_usage']['current']
            if cpu_current > 80:
                summary['status'] = 'warning'
                summary['warnings'].append(f"CPU使用率过高: {cpu_current:.1f}%")
            elif cpu_current > 95:
                summary['status'] = 'error'
                summary['errors'].append(f"CPU使用率严重过高: {cpu_current:.1f}%")
        
        # 检查内存使用率
        if 'memory_usage' in metrics:
            memory_percent = metrics['memory_usage']['percent']
            if memory_percent > 80:
                summary['status'] = 'warning'
                summary['warnings'].append(f"内存使用率过高: {memory_percent:.1f}%")
            elif memory_percent > 95:
                summary['status'] = 'error'
                summary['errors'].append(f"内存使用率严重过高: {memory_percent:.1f}%")
        
        summary['metrics'] = metrics
        return summary
    
    def reset_statistics(self):
        """重置统计数据"""
        with self.lock:
            self.fps_history.clear()
            self.latency_history.clear()
            self.cpu_history.clear()
            self.memory_history.clear()
            self.frame_timestamps.clear()
            self.processing_start_times.clear()
            self.processing_end_times.clear()
            
            self.total_frames = 0
            self.dropped_frames = 0
            self.start_time = time.time()
        
        logger.info("性能统计数据已重置")
    
    def export_metrics(self, filepath: str):
        """
        导出性能指标到文件
        
        Args:
            filepath: 导出文件路径
        """
        try:
            metrics = self.collect_metrics()
            
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, indent=2, ensure_ascii=False)
            
            logger.info(f"性能指标已导出到: {filepath}")
        except Exception as e:
            logger.error(f"性能指标导出失败: {e}")
    
    def is_enabled(self) -> bool:
        """检查监控是否启用"""
        return self.enabled
    
    def set_enabled(self, enabled: bool):
        """设置监控启用状态"""
        self.enabled = enabled
        if not enabled:
            self.reset_statistics()
        logger.info(f"性能监控{'启用' if enabled else '禁用'}")


class LatencyTracker:
    """延迟跟踪器"""
    
    def __init__(self, name: str):
        """
        初始化延迟跟踪器
        
        Args:
            name: 跟踪器名称
        """
        self.name = name
        self.start_time = None
    
    def __enter__(self):
        """进入上下文管理器"""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        if self.start_time:
            latency = (time.time() - self.start_time) * 1000
            logger.debug(f"{self.name} 延迟: {latency:.2f}ms")
    
    def get_elapsed_ms(self) -> float:
        """获取已经过的时间（毫秒）"""
        if self.start_time:
            return (time.time() - self.start_time) * 1000
        return 0.0


def measure_latency(func):
    """
    延迟测量装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        latency = (time.time() - start_time) * 1000
        logger.debug(f"{func.__name__} 执行时间: {latency:.2f}ms")
        return result
    
    return wrapper
