#!/usr/bin/env python3
"""
简单可视化器
使用matplotlib创建实时姿态数据可视化
"""

import sys
import time
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    import matplotlib.pyplot as plt
    import matplotlib.animation as animation
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("警告: matplotlib不可用，请安装matplotlib")
    MATPLOTLIB_AVAILABLE = False


class SimpleVisualizer:
    """简单可视化器类"""
    
    def __init__(self):
        """初始化可视化器"""
        self.fig = None
        self.ax = None
        self.points = None
        self.lines = None
        self.frame_count = 0
        
        # MediaPipe姿态连接关系
        self.pose_connections = [
            # 面部
            (0, 1), (1, 2), (2, 3), (3, 7),
            (0, 4), (4, 5), (5, 6), (6, 8),
            (9, 10),
            
            # 身体
            (11, 12),  # 肩膀
            (11, 13), (13, 15),  # 左臂
            (12, 14), (14, 16),  # 右臂
            (11, 23), (12, 24),  # 肩膀到臀部
            (23, 24),  # 臀部
            
            # 左腿
            (23, 25), (25, 27), (27, 29), (29, 31),
            
            # 右腿
            (24, 26), (26, 28), (28, 30), (30, 32),
            
            # 手部细节
            (15, 17), (17, 19), (19, 21), (21, 15),  # 左手
            (16, 18), (18, 20), (20, 22), (22, 16),  # 右手
        ]
        
        if MATPLOTLIB_AVAILABLE:
            self._setup_plot()
    
    def _setup_plot(self):
        """设置绘图"""
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.ax.set_xlim(0, 640)
        self.ax.set_ylim(0, 480)
        self.ax.set_aspect('equal')
        self.ax.invert_yaxis()  # 翻转Y轴以匹配图像坐标
        self.ax.set_title('实时姿态可视化')
        self.ax.set_xlabel('X坐标')
        self.ax.set_ylabel('Y坐标')
        self.ax.grid(True, alpha=0.3)
        
        # 初始化点和线
        self.points, = self.ax.plot([], [], 'ro', markersize=6, alpha=0.8)
        self.lines = []
        
        for _ in self.pose_connections:
            line, = self.ax.plot([], [], 'b-', linewidth=2, alpha=0.7)
            self.lines.append(line)
    
    def create_mock_pose_data(self, frame_count: int = 0) -> Dict[str, Any]:
        """创建模拟姿态数据"""
        landmarks = []
        
        # 创建33个关键点的模拟数据
        # 模拟一个人在屏幕中央做简单动作
        center_x, center_y = 320, 240
        
        for i in range(33):
            # 根据关键点ID设置不同的位置
            if i == 0:  # 鼻子
                x = center_x + 10 * np.sin(frame_count * 0.1)
                y = center_y - 100 + 5 * np.cos(frame_count * 0.1)
            elif i in [11, 12]:  # 肩膀
                offset = 50 if i == 11 else -50
                x = center_x + offset + 5 * np.sin(frame_count * 0.1)
                y = center_y - 50
            elif i in [13, 14]:  # 肘部
                offset = 80 if i == 13 else -80
                x = center_x + offset + 20 * np.sin(frame_count * 0.2)
                y = center_y
            elif i in [15, 16]:  # 手腕
                offset = 100 if i == 15 else -100
                x = center_x + offset + 30 * np.sin(frame_count * 0.3)
                y = center_y + 20 * np.cos(frame_count * 0.3)
            elif i in [23, 24]:  # 臀部
                offset = 30 if i == 23 else -30
                x = center_x + offset
                y = center_y + 80
            elif i in [25, 26]:  # 膝盖
                offset = 35 if i == 25 else -35
                x = center_x + offset + 5 * np.sin(frame_count * 0.15)
                y = center_y + 150
            elif i in [27, 28]:  # 脚踝
                offset = 40 if i == 27 else -40
                x = center_x + offset + 10 * np.sin(frame_count * 0.1)
                y = center_y + 220
            else:
                # 其他关键点随机分布
                x = center_x + np.random.uniform(-150, 150)
                y = center_y + np.random.uniform(-100, 200)
            
            landmark = {
                'id': i,
                'name': f'joint_{i}',
                'x': x + np.random.normal(0, 2),  # 添加噪声
                'y': y + np.random.normal(0, 2),
                'z': np.random.uniform(-50, 50),
                'visibility': np.random.uniform(0.7, 1.0)
            }
            landmarks.append(landmark)
        
        return {
            'landmarks': landmarks,
            'has_pose': True,
            'timestamp': time.time(),
            'frame_count': frame_count,
            'fps': 30.0
        }
    
    def update_visualization(self, pose_data: Dict[str, Any]):
        """更新可视化"""
        if not MATPLOTLIB_AVAILABLE or not pose_data.get('has_pose'):
            return
        
        landmarks = pose_data['landmarks']
        
        # 提取可见关键点的坐标
        visible_points = [(lm['x'], lm['y']) for lm in landmarks if lm['visibility'] > 0.5]
        
        if visible_points:
            x_coords, y_coords = zip(*visible_points)
            self.points.set_data(x_coords, y_coords)
        
        # 更新连接线
        for i, (start_idx, end_idx) in enumerate(self.pose_connections):
            if (start_idx < len(landmarks) and end_idx < len(landmarks) and
                landmarks[start_idx]['visibility'] > 0.5 and landmarks[end_idx]['visibility'] > 0.5):
                
                x_data = [landmarks[start_idx]['x'], landmarks[end_idx]['x']]
                y_data = [landmarks[start_idx]['y'], landmarks[end_idx]['y']]
                
                if i < len(self.lines):
                    self.lines[i].set_data(x_data, y_data)
        
        # 更新标题显示帧信息
        self.ax.set_title(f'实时姿态可视化 - 帧: {pose_data["frame_count"]}, FPS: {pose_data["fps"]:.1f}')
    
    def animate(self, frame):
        """动画更新函数"""
        pose_data = self.create_mock_pose_data(self.frame_count)
        self.update_visualization(pose_data)
        self.frame_count += 1
        
        return [self.points] + self.lines
    
    def start_animation(self, duration: int = 30):
        """启动动画"""
        if not MATPLOTLIB_AVAILABLE:
            print("❌ matplotlib不可用，无法启动可视化")
            return
        
        print(f"🎬 启动实时姿态可视化 (时长: {duration}秒)")
        print("💡 关闭窗口或按Ctrl+C停止")
        
        try:
            # 计算总帧数
            total_frames = duration * 30  # 30 FPS
            
            # 创建动画
            anim = animation.FuncAnimation(
                self.fig, 
                self.animate, 
                frames=total_frames,
                interval=33,  # ~30 FPS
                blit=True,
                repeat=False
            )
            
            plt.show()
            
        except KeyboardInterrupt:
            print("\n可视化已停止")
        except Exception as e:
            print(f"可视化错误: {e}")
    
    def save_static_visualization(self, filename: str = "pose_visualization.png"):
        """保存静态可视化图像"""
        if not MATPLOTLIB_AVAILABLE:
            print("❌ matplotlib不可用，无法保存图像")
            return
        
        # 创建一帧数据
        pose_data = self.create_mock_pose_data(100)  # 使用固定帧数
        self.update_visualization(pose_data)
        
        # 保存图像
        self.fig.savefig(filename, dpi=150, bbox_inches='tight')
        print(f"✅ 静态可视化已保存: {filename}")
    
    def create_data_plot(self, duration: int = 10):
        """创建数据趋势图"""
        if not MATPLOTLIB_AVAILABLE:
            print("❌ matplotlib不可用，无法创建数据图")
            return
        
        print(f"📊 生成 {duration} 秒的数据趋势图...")
        
        # 收集数据
        timestamps = []
        nose_x = []
        nose_y = []
        left_hand_x = []
        left_hand_y = []
        right_hand_x = []
        right_hand_y = []
        
        frames = duration * 30
        for i in range(frames):
            pose_data = self.create_mock_pose_data(i)
            landmarks = pose_data['landmarks']
            
            timestamps.append(i / 30.0)  # 转换为秒
            
            # 鼻子 (关键点0)
            nose_x.append(landmarks[0]['x'])
            nose_y.append(landmarks[0]['y'])
            
            # 左手 (关键点15)
            left_hand_x.append(landmarks[15]['x'])
            left_hand_y.append(landmarks[15]['y'])
            
            # 右手 (关键点16)
            right_hand_x.append(landmarks[16]['x'])
            right_hand_y.append(landmarks[16]['y'])
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('关键点运动趋势分析')
        
        # 鼻子X坐标
        axes[0, 0].plot(timestamps, nose_x, 'r-', label='鼻子X')
        axes[0, 0].set_title('鼻子X坐标变化')
        axes[0, 0].set_xlabel('时间 (秒)')
        axes[0, 0].set_ylabel('X坐标')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 鼻子Y坐标
        axes[0, 1].plot(timestamps, nose_y, 'g-', label='鼻子Y')
        axes[0, 1].set_title('鼻子Y坐标变化')
        axes[0, 1].set_xlabel('时间 (秒)')
        axes[0, 1].set_ylabel('Y坐标')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 手部X坐标对比
        axes[1, 0].plot(timestamps, left_hand_x, 'b-', label='左手X')
        axes[1, 0].plot(timestamps, right_hand_x, 'r-', label='右手X')
        axes[1, 0].set_title('手部X坐标对比')
        axes[1, 0].set_xlabel('时间 (秒)')
        axes[1, 0].set_ylabel('X坐标')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 手部Y坐标对比
        axes[1, 1].plot(timestamps, left_hand_y, 'b-', label='左手Y')
        axes[1, 1].plot(timestamps, right_hand_y, 'r-', label='右手Y')
        axes[1, 1].set_title('手部Y坐标对比')
        axes[1, 1].set_xlabel('时间 (秒)')
        axes[1, 1].set_ylabel('Y坐标')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图像
        filename = "motion_trends.png"
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        print(f"✅ 趋势图已保存: {filename}")
        
        plt.show()


async def main():
    """主函数"""
    print("🎨 简单姿态可视化器")
    print("=" * 40)
    
    if not MATPLOTLIB_AVAILABLE:
        print("❌ 需要安装matplotlib才能运行可视化")
        print("安装命令: pip install matplotlib")
        return
    
    visualizer = SimpleVisualizer()
    
    print("\n选择功能:")
    print("1. 实时动画可视化")
    print("2. 保存静态图像")
    print("3. 生成数据趋势图")
    print("0. 退出")
    
    try:
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "1":
            duration = int(input("动画时长 (秒, 默认30): ") or "30")
            visualizer.start_animation(duration)
            
        elif choice == "2":
            filename = input("文件名 (默认pose_visualization.png): ").strip() or "pose_visualization.png"
            visualizer.save_static_visualization(filename)
            
        elif choice == "3":
            duration = int(input("数据时长 (秒, 默认10): ") or "10")
            visualizer.create_data_plot(duration)
            
        elif choice == "0":
            print("👋 再见!")
            
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"❌ 发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
