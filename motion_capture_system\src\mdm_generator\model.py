"""
动作生成模型
提供LSTM和简单预测模型的实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from loguru import logger


class LSTMMotionModel(nn.Module):
    """LSTM动作预测模型"""
    
    def __init__(self, 
                 input_size: int = 99,  # 33个关键点 * 3个坐标
                 hidden_size: int = 128,
                 num_layers: int = 2,
                 dropout: float = 0.1,
                 sequence_length: int = 10,
                 prediction_length: int = 5):
        """
        初始化LSTM模型
        
        Args:
            input_size: 输入特征维度
            hidden_size: 隐藏层大小
            num_layers: LSTM层数
            dropout: Dropout率
            sequence_length: 输入序列长度
            prediction_length: 预测序列长度
        """
        super(LSTMMotionModel, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.sequence_length = sequence_length
        self.prediction_length = prediction_length
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # 输出层
        self.output_layer = nn.Linear(hidden_size, input_size)
        
        # Dropout层
        self.dropout = nn.Dropout(dropout)
        
        logger.info(f"LSTM模型初始化完成: input_size={input_size}, hidden_size={hidden_size}")
    
    def forward(self, x: torch.Tensor, hidden: Optional[Tuple[torch.Tensor, torch.Tensor]] = None) -> Tuple[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, sequence_length, input_size]
            hidden: 隐藏状态（可选）
            
        Returns:
            输出张量和新的隐藏状态
        """
        # LSTM前向传播
        lstm_out, hidden = self.lstm(x, hidden)
        
        # 应用dropout
        lstm_out = self.dropout(lstm_out)
        
        # 输出层
        output = self.output_layer(lstm_out)
        
        return output, hidden
    
    def predict_sequence(self, input_sequence: torch.Tensor) -> torch.Tensor:
        """
        预测未来序列
        
        Args:
            input_sequence: 输入序列 [batch_size, sequence_length, input_size]
            
        Returns:
            预测序列 [batch_size, prediction_length, input_size]
        """
        self.eval()
        with torch.no_grad():
            batch_size = input_sequence.size(0)
            predictions = []
            
            # 初始化隐藏状态
            hidden = None
            
            # 使用输入序列初始化LSTM状态
            _, hidden = self.forward(input_sequence, hidden)
            
            # 使用最后一个时间步作为起始点
            current_input = input_sequence[:, -1:, :]  # [batch_size, 1, input_size]
            
            # 逐步预测
            for _ in range(self.prediction_length):
                output, hidden = self.forward(current_input, hidden)
                predictions.append(output)
                current_input = output  # 使用预测结果作为下一步输入
            
            # 拼接预测结果
            predicted_sequence = torch.cat(predictions, dim=1)
            
        return predicted_sequence
    
    def init_hidden(self, batch_size: int, device: torch.device) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        初始化隐藏状态
        
        Args:
            batch_size: 批次大小
            device: 设备
            
        Returns:
            初始化的隐藏状态
        """
        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_size, device=device)
        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_size, device=device)
        return h0, c0


class SimpleMotionModel:
    """简单动作预测模型（基于插值和外推）"""
    
    def __init__(self, sequence_length: int = 10, prediction_length: int = 5):
        """
        初始化简单模型
        
        Args:
            sequence_length: 输入序列长度
            prediction_length: 预测序列长度
        """
        self.sequence_length = sequence_length
        self.prediction_length = prediction_length
        
        logger.info("简单动作预测模型初始化完成")
    
    def predict_linear_extrapolation(self, sequence: np.ndarray) -> np.ndarray:
        """
        线性外推预测
        
        Args:
            sequence: 输入序列 [sequence_length, feature_dim]
            
        Returns:
            预测序列 [prediction_length, feature_dim]
        """
        if len(sequence) < 2:
            # 如果序列太短，返回最后一帧的重复
            last_frame = sequence[-1] if len(sequence) > 0 else np.zeros(sequence.shape[1])
            return np.tile(last_frame, (self.prediction_length, 1))
        
        # 计算最后两帧的差值（速度）
        velocity = sequence[-1] - sequence[-2]
        
        # 线性外推
        predictions = []
        current_frame = sequence[-1]
        
        for i in range(self.prediction_length):
            next_frame = current_frame + velocity * (i + 1)
            predictions.append(next_frame)
        
        return np.array(predictions)
    
    def predict_polynomial_extrapolation(self, sequence: np.ndarray, degree: int = 2) -> np.ndarray:
        """
        多项式外推预测
        
        Args:
            sequence: 输入序列 [sequence_length, feature_dim]
            degree: 多项式度数
            
        Returns:
            预测序列 [prediction_length, feature_dim]
        """
        seq_len, feature_dim = sequence.shape
        
        if seq_len < degree + 1:
            # 序列太短，使用线性外推
            return self.predict_linear_extrapolation(sequence)
        
        # 时间索引
        t = np.arange(seq_len)
        t_future = np.arange(seq_len, seq_len + self.prediction_length)
        
        predictions = np.zeros((self.prediction_length, feature_dim))
        
        # 对每个特征维度进行多项式拟合
        for dim in range(feature_dim):
            try:
                # 多项式拟合
                coeffs = np.polyfit(t, sequence[:, dim], degree)
                # 预测未来值
                predictions[:, dim] = np.polyval(coeffs, t_future)
            except np.RankWarning:
                # 拟合失败，使用线性外推
                if seq_len >= 2:
                    velocity = sequence[-1, dim] - sequence[-2, dim]
                    for i in range(self.prediction_length):
                        predictions[i, dim] = sequence[-1, dim] + velocity * (i + 1)
                else:
                    predictions[:, dim] = sequence[-1, dim]
        
        return predictions
    
    def predict_moving_average(self, sequence: np.ndarray, window_size: int = 3) -> np.ndarray:
        """
        移动平均预测
        
        Args:
            sequence: 输入序列 [sequence_length, feature_dim]
            window_size: 移动平均窗口大小
            
        Returns:
            预测序列 [prediction_length, feature_dim]
        """
        seq_len, feature_dim = sequence.shape
        
        if seq_len < window_size:
            window_size = seq_len
        
        # 计算最后几帧的平均值
        avg_frame = np.mean(sequence[-window_size:], axis=0)
        
        # 计算趋势（如果有足够的数据）
        if seq_len >= 2:
            trend = sequence[-1] - sequence[-2]
        else:
            trend = np.zeros(feature_dim)
        
        # 生成预测
        predictions = []
        for i in range(self.prediction_length):
            # 结合平均值和趋势
            predicted_frame = avg_frame + trend * (i + 1) * 0.5  # 减弱趋势影响
            predictions.append(predicted_frame)
        
        return np.array(predictions)


class MotionEnhancer:
    """动作增强器"""
    
    def __init__(self, smoothing_factor: float = 0.1):
        """
        初始化动作增强器
        
        Args:
            smoothing_factor: 平滑因子
        """
        self.smoothing_factor = smoothing_factor
        
        logger.info("动作增强器初始化完成")
    
    def smooth_motion(self, sequence: np.ndarray) -> np.ndarray:
        """
        平滑动作序列
        
        Args:
            sequence: 输入序列 [sequence_length, feature_dim]
            
        Returns:
            平滑后的序列
        """
        if len(sequence) <= 1:
            return sequence
        
        smoothed = sequence.copy()
        
        # 应用指数平滑
        for i in range(1, len(sequence)):
            smoothed[i] = (1 - self.smoothing_factor) * smoothed[i-1] + self.smoothing_factor * sequence[i]
        
        return smoothed
    
    def add_noise(self, sequence: np.ndarray, noise_level: float = 0.01) -> np.ndarray:
        """
        添加噪声（用于数据增强）
        
        Args:
            sequence: 输入序列
            noise_level: 噪声水平
            
        Returns:
            添加噪声后的序列
        """
        noise = np.random.normal(0, noise_level, sequence.shape)
        return sequence + noise
    
    def interpolate_missing(self, sequence: np.ndarray, confidence_threshold: float = 0.5) -> np.ndarray:
        """
        插值缺失的关键点
        
        Args:
            sequence: 输入序列
            confidence_threshold: 置信度阈值
            
        Returns:
            插值后的序列
        """
        # 这里假设sequence的最后一维是置信度
        # 实际实现中需要根据数据格式调整
        interpolated = sequence.copy()
        
        # 简单的线性插值实现
        for dim in range(sequence.shape[-1]):
            for t in range(len(sequence)):
                if t > 0 and t < len(sequence) - 1:
                    # 检查前后帧是否有效
                    prev_valid = True  # 这里需要根据实际置信度判断
                    next_valid = True
                    
                    if prev_valid and next_valid:
                        # 线性插值
                        interpolated[t, dim] = (sequence[t-1, dim] + sequence[t+1, dim]) / 2
        
        return interpolated


def create_model(model_type: str, config: Dict) -> Any:
    """
    创建模型实例
    
    Args:
        model_type: 模型类型
        config: 模型配置
        
    Returns:
        模型实例
    """
    if model_type == "lstm":
        lstm_config = config.get('lstm', {})
        return LSTMMotionModel(
            input_size=lstm_config.get('input_size', 99),
            hidden_size=lstm_config.get('hidden_size', 128),
            num_layers=lstm_config.get('num_layers', 2),
            dropout=lstm_config.get('dropout', 0.1),
            sequence_length=lstm_config.get('sequence_length', 10),
            prediction_length=lstm_config.get('prediction_length', 5)
        )
    elif model_type == "simple":
        return SimpleMotionModel(
            sequence_length=config.get('sequence_length', 10),
            prediction_length=config.get('prediction_length', 5)
        )
    else:
        logger.warning(f"未知的模型类型: {model_type}，使用简单模型")
        return SimpleMotionModel()
