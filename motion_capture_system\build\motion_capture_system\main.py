#!/usr/bin/env python3
"""
实时动作捕捉和生成系统主程序
包含MediaPipe姿态检测、MDM动作生成和OSC通信功能
"""

import asyncio
import logging
import signal
import sys
import threading
import time
from pathlib import Path
from typing import Optional

import yaml
from loguru import logger

# 导入自定义模块
from src.mediapipe_capture.pose_detector import PoseDetector
from src.mdm_generator.motion_predictor import MotionPredictor
from src.osc_communication.osc_sender import OSCSender
from src.utils.performance import PerformanceMonitor
from src.utils.filters import DataFilter


class MotionCaptureSystem:
    """实时动作捕捉和生成系统主类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化系统"""
        self.config = self._load_config(config_path)
        self._setup_logging()
        
        # 初始化组件
        self.pose_detector: Optional[PoseDetector] = None
        self.motion_predictor: Optional[MotionPredictor] = None
        self.osc_sender: Optional[OSCSender] = None
        self.data_filter: Optional[DataFilter] = None
        self.performance_monitor: Optional[PerformanceMonitor] = None
        
        # 控制变量
        self.running = False
        self.threads = []
        
        # 数据队列
        self.pose_queue = asyncio.Queue(maxsize=self.config['performance']['max_queue_size'])
        self.processed_queue = asyncio.Queue(maxsize=self.config['performance']['max_queue_size'])
        
        logger.info("动作捕捉系统初始化完成")
    
    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            sys.exit(1)
    
    def _setup_logging(self):
        """设置日志系统"""
        log_config = self.config['logging']
        
        # 创建日志目录
        log_file = Path(log_config['file'])
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 配置loguru
        logger.remove()  # 移除默认处理器
        logger.add(
            sys.stderr,
            level=log_config['level'],
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
        logger.add(
            log_config['file'],
            level=log_config['level'],
            rotation=log_config['max_size'],
            retention=log_config['backup_count'],
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
        )
    
    async def initialize_components(self):
        """初始化所有组件"""
        try:
            # 初始化姿态检测器
            self.pose_detector = PoseDetector(self.config['camera'], self.config['mediapipe'])
            logger.info("姿态检测器初始化完成")
            
            # 初始化数据滤波器
            self.data_filter = DataFilter(self.config['data_processing'])
            logger.info("数据滤波器初始化完成")
            
            # 初始化动作预测器
            if self.config['mdm']['enabled']:
                self.motion_predictor = MotionPredictor(self.config['mdm'])
                logger.info("动作预测器初始化完成")
            
            # 初始化OSC发送器
            if self.config['osc']['enabled']:
                self.osc_sender = OSCSender(self.config['osc'])
                logger.info("OSC发送器初始化完成")
            
            # 初始化性能监控器
            if self.config['logging']['performance_monitoring']['enabled']:
                self.performance_monitor = PerformanceMonitor(self.config['logging']['performance_monitoring'])
                logger.info("性能监控器初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
            raise
    
    async def start(self):
        """启动系统"""
        logger.info("启动动作捕捉系统...")
        
        try:
            await self.initialize_components()
            self.running = True
            
            # 启动各个处理线程
            tasks = []
            
            # 姿态捕捉任务
            tasks.append(asyncio.create_task(self._capture_loop()))
            
            # 数据处理任务
            tasks.append(asyncio.create_task(self._processing_loop()))
            
            # OSC发送任务
            if self.osc_sender:
                tasks.append(asyncio.create_task(self._osc_loop()))
            
            # 性能监控任务
            if self.performance_monitor:
                tasks.append(asyncio.create_task(self._monitor_loop()))
            
            logger.info("所有任务启动完成")
            
            # 等待所有任务完成
            await asyncio.gather(*tasks)
            
        except Exception as e:
            logger.error(f"系统启动失败: {e}")
            await self.stop()
    
    async def stop(self):
        """停止系统"""
        logger.info("正在停止动作捕捉系统...")
        self.running = False
        
        # 清理资源
        if self.pose_detector:
            self.pose_detector.cleanup()
        
        if self.osc_sender:
            self.osc_sender.cleanup()
        
        logger.info("动作捕捉系统已停止")
    
    async def _capture_loop(self):
        """姿态捕捉循环"""
        logger.info("姿态捕捉循环启动")
        
        while self.running:
            try:
                # 捕捉姿态数据
                pose_data = await self.pose_detector.get_pose_async()
                
                if pose_data is not None:
                    # 添加时间戳
                    pose_data['timestamp'] = time.time()
                    
                    # 将数据放入队列
                    try:
                        self.pose_queue.put_nowait(pose_data)
                    except asyncio.QueueFull:
                        if self.config['performance']['enable_frame_skip']:
                            # 跳帧：移除最旧的数据
                            try:
                                self.pose_queue.get_nowait()
                                self.pose_queue.put_nowait(pose_data)
                            except asyncio.QueueEmpty:
                                pass
                        else:
                            logger.warning("姿态数据队列已满")
                
                # 控制帧率
                await asyncio.sleep(1.0 / self.config['camera']['fps'])
                
            except Exception as e:
                logger.error(f"姿态捕捉错误: {e}")
                await asyncio.sleep(0.1)
    
    async def _processing_loop(self):
        """数据处理循环"""
        logger.info("数据处理循环启动")
        
        while self.running:
            try:
                # 从队列获取姿态数据
                pose_data = await self.pose_queue.get()
                
                # 数据滤波和预处理
                filtered_data = self.data_filter.process(pose_data)
                
                # 动作生成/预测
                if self.motion_predictor and self.config['mdm']['enabled']:
                    enhanced_data = await self.motion_predictor.predict_async(filtered_data)
                else:
                    enhanced_data = filtered_data
                
                # 将处理后的数据放入输出队列
                try:
                    self.processed_queue.put_nowait(enhanced_data)
                except asyncio.QueueFull:
                    # 移除最旧的数据
                    try:
                        self.processed_queue.get_nowait()
                        self.processed_queue.put_nowait(enhanced_data)
                    except asyncio.QueueEmpty:
                        pass
                
            except Exception as e:
                logger.error(f"数据处理错误: {e}")
                await asyncio.sleep(0.01)
    
    async def _osc_loop(self):
        """OSC发送循环"""
        logger.info("OSC发送循环启动")
        
        while self.running:
            try:
                # 从队列获取处理后的数据
                processed_data = await self.processed_queue.get()
                
                # 发送OSC消息
                await self.osc_sender.send_pose_data_async(processed_data)
                
            except Exception as e:
                logger.error(f"OSC发送错误: {e}")
                await asyncio.sleep(0.01)
    
    async def _monitor_loop(self):
        """性能监控循环"""
        logger.info("性能监控循环启动")
        
        while self.running:
            try:
                # 收集性能指标
                metrics = self.performance_monitor.collect_metrics()
                
                # 记录性能数据
                if metrics:
                    logger.info(f"性能指标: {metrics}")
                
                # 等待下一次监控
                await asyncio.sleep(self.config['logging']['performance_monitoring']['log_interval'])
                
            except Exception as e:
                logger.error(f"性能监控错误: {e}")
                await asyncio.sleep(1)


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info("接收到停止信号，正在关闭系统...")
    sys.exit(0)


async def main():
    """主函数"""
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并启动系统
    system = MotionCaptureSystem()
    
    try:
        await system.start()
    except KeyboardInterrupt:
        logger.info("用户中断，正在停止系统...")
    except Exception as e:
        logger.error(f"系统运行错误: {e}")
    finally:
        await system.stop()


if __name__ == "__main__":
    asyncio.run(main())
